{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Supplier Details - {{ supplier.name }} | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .supplier-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .info-card {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        border: none;
        margin-bottom: 1.5rem;
    }
    .info-card .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
    }
    .supplier-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        font-size: 1.5rem;
        margin-right: 1.5rem;
    }
    .balance-positive { color: #28a745; }
    .balance-negative { color: #dc3545; }
    .balance-zero { color: #6c757d; }
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        text-align: center;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .action-buttons .btn {
        margin: 0.25rem;
        border-radius: 8px;
        padding: 0.5rem 1rem;
    }
    .transaction-table {
        font-size: 0.9rem;
    }
    .rating-stars {
        color: #ffc107;
        font-size: 1.2rem;
    }
    .rating-display {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Supplier Header -->
    <div class="supplier-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="supplier-avatar" style="background-color: #667eea;">
                        {{ supplier.name|slice:':2'|upper }}
                    </div>
                    <div>
                        <h1 class="mb-1">{{ supplier.name }}</h1>
                        {% if supplier.company_name %}
                        <p class="mb-0 opacity-75">{{ supplier.company_name }}</p>
                        {% endif %}
                        {% if supplier.rating %}
                        <div class="rating-display mt-2">
                            <div class="rating-stars">
                                {% for i in "12345"|make_list %}
                                    {% if forloop.counter <= supplier.rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="badge bg-light text-dark">{{ supplier.rating }}/5</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <span class="badge 
                    {% if supplier.is_active %}bg-success
                    {% else %}bg-secondary{% endif %} p-2">
                    {% if supplier.is_active %}Active Supplier{% else %}Inactive{% endif %}
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Supplier Information -->
        <div class="col-lg-8">
            <!-- Contact Information -->
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-address-card me-2"></i>Contact معلومات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Phone Number</label>
                                <p class="fw-bold">
                                    <i class="fas fa-phone me-2"></i>{{ supplier.phone }}
                                </p>
                            </div>
                            {% if supplier.email %}
                            <div class="mb-3">
                                <label class="form-label text-muted">Email Address</label>
                                <p>
                                    <i class="fas fa-envelope me-2"></i>{{ supplier.email }}
                                </p>
                            </div>
                            {% endif %}
                            {% if supplier.website %}
                            <div class="mb-3">
                                <label class="form-label text-muted">Website</label>
                                <p>
                                    <i class="fas fa-globe me-2"></i>
                                    <a href="{{ supplier.website }}" target="_blank" class="text-decoration-none">
                                        {{ supplier.website }}
                                    </a>
                                </p>
                            </div>
                            {% endif %}
                            {% if supplier.contact_person %}
                            <div class="mb-3">
                                <label class="form-label text-muted">Contact Person</label>
                                <p>
                                    <i class="fas fa-user me-2"></i>{{ supplier.contact_person }}
                                </p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if supplier.address %}
                            <div class="mb-3">
                                <label class="form-label text-muted">إضافةress</label>
                                <p>
                                    <i class="fas fa-map-marker-alt me-2"></i>{{ supplier.address }}
                                </p>
                            </div>
                            {% endif %}
                            {% if supplier.city %}
                            <div class="mb-3">
                                <label class="form-label text-muted">المدينة</label>
                                <p>{{ supplier.city }}</p>
                            </div>
                            {% endif %}
                            {% if supplier.tax_number %}
                            <div class="mb-3">
                                <label class="form-label text-muted">Tax Number</label>
                                <p>{{ supplier.tax_number }}</p>
                            </div>
                            {% endif %}
                            {% if supplier.bank_details %}
                            <div class="mb-3">
                                <label class="form-label text-muted">Bank Details</label>
                                <p>{{ supplier.bank_details }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Information -->
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Financial معلومات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Current الرصيد</label>
                                <p class="fw-bold 
                                    {% if supplier.balance > 0 %}balance-positive
                                    {% elif supplier.balance < 0 %}balance-negative
                                    {% else %}balance-zero{% endif %}">
                                    ${{ supplier.balance|floatformat:2 }}
                                </p>
                                <small class="text-muted">
                                    {% if supplier.balance > 0 %}
                                        Amount we owe to supplier
                                    {% elif supplier.balance < 0 %}
                                        Amount supplier owes us
                                    {% else %}
                                        No outstanding balance
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Credit Limit</label>
                                <p class="fw-bold">${{ supplier.credit_limit|default:0|floatformat:2 }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">الدفع Terms</label>
                                <p>{{ supplier.payment_terms|default:"Not specified" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase History -->
            <div class="card info-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Purchase History</h5>
                    <small class="text-muted">Recent transactions</small>
                </div>
                <div class="card-body p-0">
                    {% comment %}
                    <!-- This will be implemented when purchases module is ready -->
                    {% if recent_purchases %}
                    <div class="table-responsive">
                        <table class="table table-hover transaction-table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الطلب #</th>
                                    <th>المبلغ</th>
                                    <th>الدفع</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for purchase in recent_purchases %}
                                <tr>
                                    <td>{{ purchase.order_date }}</td>
                                    <td>{{ purchase.order_number }}</td>
                                    <td>${{ purchase.total_amount }}</td>
                                    <td>${{ purchase.paid_amount }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ purchase.status }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    {% endcomment %}
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No purchase transactions recorded yet.</p>
                        <a href="#" class="btn btn-primary">إنشاء First Purchase الطلب</a>
                    </div>
                    {% comment %}
                    {% endif %}
                    {% endcomment %}
                </div>
            </div>

            <!-- Products Supplied -->
            <div class="card info-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>المنتج المورد</h5>
                    <small class="text-muted">منتج من هذا المورد</small>
                </div>
                <div class="card-body p-0">
                    {% comment %}
                    <!-- This will show products when supplier-product relationship is established -->
                    {% endcomment %}
                    <div class="text-center py-4">
                        <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No products linked to this supplier yet.</p>
                        <a href="#" class="btn btn-outline-primary">Link المنتجات</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Stats -->
        <div class="col-lg-4">
            <!-- Supplier Statistics -->
            <div class="stat-card">
                <div class="stat-value">{{ supplier.total_orders|default:0 }}</div>
                <div class="stat-label">الإجمالي Orders</div>
            </div>

            <div class="stat-card">
                <div class="stat-value">${{ supplier.total_purchases|default:0 }}</div>
                <div class="stat-label">الإجمالي Purchases</div>
            </div>

            <!-- Quick Info -->
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Quick معلومات</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">المورد Since</label>
                        <p class="fw-bold">{{ supplier.created_at|date:"M d, Y" }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">الأخير Purchase</label>
                        <p>
                            {% if supplier.last_purchase_date %}
                                {{ supplier.last_purchase_date|date:"M d, Y" }}
                            {% else %}
                                No purchases yet
                            {% endif %}
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Average Order Value</label>
                        <p>${{ supplier.avg_order_value|default:0|floatformat:2 }}</p>
                    </div>
                    {% if supplier.lead_time %}
                    <div class="mb-3">
                        <label class="form-label text-muted">Lead Time</label>
                        <p>{{ supplier.lead_time }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Rating Card -->
            {% if supplier.rating %}
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-star me-2"></i>المورد Rating</h6>
                </div>
                <div class="card-body text-center">
                    <div class="rating-stars mb-2">
                        {% for i in "12345"|make_list %}
                            {% if forloop.counter <= supplier.rating %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <p class="mb-0">{{ supplier.rating }}/5 Stars</p>
                    <button class="btn btn-outline-primary btn-sm mt-2" data-bs-toggle="modal" data-bs-target="#ratingModal">
                        Update Rating
                    </button>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tools me-2"></i>الإجراءات</h6>
                </div>
                <div class="card-body action-buttons">
                    <a href="#" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>تعديل Supplier
                    </a>
                    <a href="#" class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-1"></i>New Purchase Order
                    </a>
                    <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#paymentModal">
                        <i class="fas fa-credit-card me-1"></i>تسجيل دفعة
                    </button>
                    <a href="#" class="btn btn-warning btn-sm">
                        <i class="fas fa-file-invoice me-1"></i>عرض Orders
                    </a>
                    <a href="{% url 'inventory:supplier_list' %}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>رجوع to List
                    </a>
                </div>
            </div>

            <!-- Supplier Notes -->
            {% if supplier.notes %}
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ supplier.notes }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Timestamps -->
            <div class="card info-card">
                <div class="card-body">
                    <small class="text-muted">
                        <div class="mb-1">
                            <strong>إنشاءd:</strong> {{ supplier.created_at|date:"M d, Y H:i" }}
                        </div>
                        <div>
                            <strong>تحديثd:</strong> {{ supplier.updated_at|date:"M d, Y H:i" }}
                        </div>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسجيل دفعة to المورد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">الدفع المبلغ</label>
                        <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الدفع Method</label>
                        <select class="form-select" name="payment_method" required>
                            <option value="">Select payment method</option>
                            <option value="cash">نقدي</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">Check</option>
                            <option value="card">Credit/Debit Card</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Reference</label>
                        <input type="text" class="form-control" name="reference" placeholder="Transaction ID, check number, etc.">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" class="btn btn-success">Record الدفع</button>
            </div>
        </div>
    </div>
</div>

<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rate المورد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">Rating</label>
                        <div class="rating-input">
                            {% for i in "12345"|make_list %}
                            <input type="radio" name="rating" value="{{ forloop.counter }}" id="star{{ forloop.counter }}" 
                                   {% if supplier.rating == forloop.counter %}checked{% endif %}>
                            <label for="star{{ forloop.counter }}" class="star-label">
                                <i class="fas fa-star"></i>
                            </label>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Comments</label>
                        <textarea class="form-control" name="rating_comments" rows="3" 
                                  placeholder="Optional comments about this supplier"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" class="btn btn-primary">حفظ Rating</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate avatar color based on name
    const avatar = document.querySelector('.supplier-avatar');
    if (avatar) {
        const name = avatar.textContent.trim();
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'];
        const colorIndex = name.charCodeAt(0) % colors.length;
        avatar.style.backgroundColor = colors[colorIndex];
    }

    // Rating input functionality
    const ratingInputs = document.querySelectorAll('.rating-input input[type="radio"]');
    const starLabels = document.querySelectorAll('.star-label');

    ratingInputs.forEach((input, index) => {
        input.addEventListener('change', function() {
            starLabels.forEach((label, labelIndex) => {
                if (labelIndex <= index) {
                    label.style.color = '#ffc107';
                } else {
                    label.style.color = '#e4e5e9';
                }
            });
        });
    });
});
</script>

<style>
.rating-input {
    display: flex;
    gap: 0.25rem;
}

.rating-input input[type="radio"] {
    display: none;
}

.star-label {
    cursor: pointer;
    font-size: 1.5rem;
    color: #e4e5e9;
    transition: color 0.2s ease;
}

.star-label:hover {
    color: #ffc107;
}

.rating-input input[type="radio"]:checked ~ .star-label,
.rating-input input[type="radio"]:checked + .star-label {
    color: #ffc107;
}
</style>
{% endblock %}