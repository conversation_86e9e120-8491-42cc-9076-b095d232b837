{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}لوحة تحكم الإعدادات - SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .settings-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        padding: 2rem;
        margin-bottom: 2rem;
        transition: transform 0.2s ease;
    }
    .settings-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }
    .settings-card .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }
    .settings-card h5 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    .settings-card p {
        color: #6c757d;
        margin-bottom: 1.5rem;
    }
    .settings-card .btn {
        border-radius: 10px;
        font-weight: 600;
        padding: 0.6rem 1.5rem;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .page-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    .quick-actions {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .quick-actions h4 {
        margin-bottom: 1.5rem;
    }
    .quick-actions .btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        margin: 0.25rem;
    }
    .quick-actions .btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
    }
    .shop-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .shop-logo {
        width: 80px;
        height: 80px;
        border-radius: 15px;
        object-fit: cover;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="mb-2">لوحة تحكم الإعدادات</h1>
        <p class="text-muted mb-0">إدارة الإعدادات العامة والروابط السريعة للنظام</p>
    </div>

    <!-- Shop Information -->
    <div class="shop-info">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                {% if settings.logo %}
                    <img src="{{ settings.logo.url }}" alt="شعار المحل" class="shop-logo">
                {% else %}
                    <div class="shop-logo bg-white d-flex align-items-center justify-content-center">
                        <i class="fas fa-store text-info fa-2x"></i>
                    </div>
                {% endif %}
            </div>
            <div class="col-md-10">
                <h3 class="mb-2">{{ settings.shop_name }}</h3>
                <div class="row">
                    <div class="col-md-6">
                        {% if settings.phone %}
                            <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ settings.phone }}</p>
                        {% endif %}
                        {% if settings.email %}
                            <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ settings.email }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if settings.address %}
                            <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i>{{ settings.address }}</p>
                        {% endif %}
                        {% if settings.city %}
                            <p class="mb-1"><i class="fas fa-city me-2"></i>{{ settings.city }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ total_products }}</div>
            <div class="stat-label">إجمالي المنتجات</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ total_customers }}</div>
            <div class="stat-label">إجمالي العملاء</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ total_suppliers }}</div>
            <div class="stat-label">إجمالي الموردين</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ total_categories }}</div>
            <div class="stat-label">إجمالي الفئات</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ total_units }}</div>
            <div class="stat-label">إجمالي الوحدات</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h4><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h4>
        <div class="row">
            <div class="col-md-6">
                <h6 class="mb-3">إضافة جديد:</h6>
                <a href="{% url 'inventory:product_create' %}" class="btn btn-outline-light">
                    <i class="fas fa-plus me-2"></i>منتج جديد
                </a>
                <a href="{% url 'inventory:customer_create' %}" class="btn btn-outline-light">
                    <i class="fas fa-user-plus me-2"></i>عميل جديد
                </a>
                <a href="{% url 'inventory:supplier_create' %}" class="btn btn-outline-light">
                    <i class="fas fa-truck me-2"></i>مورد جديد
                </a>
                <a href="{% url 'inventory:category_create' %}" class="btn btn-outline-light">
                    <i class="fas fa-tags me-2"></i>فئة جديدة
                </a>
                <a href="{% url 'inventory:unit_create' %}" class="btn btn-outline-light">
                    <i class="fas fa-ruler me-2"></i>وحدة جديدة
                </a>
            </div>
            <div class="col-md-6">
                <h6 class="mb-3">الفواتير:</h6>
                <a href="{% url 'inventory:invoice_create' %}" class="btn btn-outline-light">
                    <i class="fas fa-file-invoice me-2"></i>فاتورة جديدة
                </a>
                <a href="{% url 'inventory:invoice_list' %}" class="btn btn-outline-light">
                    <i class="fas fa-list me-2"></i>جميع الفواتير
                </a>
            </div>
        </div>
    </div>

    <!-- Settings Cards -->
    <div class="row">
        <div class="col-lg-6">
            <div class="settings-card">
                <div class="card-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <i class="fas fa-store"></i>
                </div>
                <h5>إعدادات المحل</h5>
                <p>إدارة معلومات المحل التجاري والشعار والعنوان وبيانات الاتصال</p>
                <a href="{% url 'inventory:shop_settings' %}" class="btn btn-primary">
                    <i class="fas fa-cog me-2"></i>إدارة الإعدادات
                </a>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="settings-card">
                <div class="card-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <i class="fas fa-tags"></i>
                </div>
                <h5>إدارة الفئات</h5>
                <p>إضافة وتعديل فئات المنتجات وتنظيمها حسب نوع المركبة</p>
                <a href="{% url 'inventory:category_list' %}" class="btn btn-success">
                    <i class="fas fa-list me-2"></i>إدارة الفئات
                </a>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="settings-card">
                <div class="card-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                    <i class="fas fa-ruler"></i>
                </div>
                <h5>إدارة الوحدات</h5>
                <p>إضافة وتعديل وحدات القياس المستخدمة في المنتجات</p>
                <a href="{% url 'inventory:unit_list' %}" class="btn btn-warning">
                    <i class="fas fa-list me-2"></i>إدارة الوحدات
                </a>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="settings-card">
                <div class="card-icon" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <h5>إدارة الفواتير</h5>
                <p>إنشاء وطباعة فواتير البيع والشراء والاستعلام عنها</p>
                <a href="{% url 'inventory:invoice_list' %}" class="btn btn-danger">
                    <i class="fas fa-list me-2"></i>إدارة الفواتير
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
