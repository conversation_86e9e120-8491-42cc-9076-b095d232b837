{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Quick Sale | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .quick-sale-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #28a745;
    }
    .calculation-display {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        position: sticky;
        top: 2rem;
    }
    .amount-display {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    .breakdown-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .breakdown-item:last-child {
        border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        font-weight: bold;
        margin-top: 1rem;
        font-size: 1.1rem;
    }
    .product-info {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    .stock-warning {
        background: #ffebee;
        border: 1px solid #ffcdd2;
        color: #c62828;
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 0.5rem;
    }
    .profit-indicator {
        background: #e8f5e8;
        border: 1px solid #c8e6c9;
        color: #2e7d32;
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 0.5rem;
    }
    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    .btn-quick-sale {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 10px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: bold;
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        transition: all 0.3s ease;
    }
    .btn-quick-sale:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }
    .quick-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    .quick-actions .btn {
        flex: 1;
        padding: 1rem;
        border-radius: 10px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="fas fa-bolt me-3"></i>بيع سريع
                </h1>
                <p class="mb-0 opacity-75">Fast cash transactions for immediate sales</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'sales:sale_list' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>رجوع إلي البيع
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="{% url 'sales:sale_create' %}" class="btn btn-outline-primary">
            <i class="fas fa-plus me-2"></i>Detailed Sale
        </a>
        <a href="{% url 'inventory:product_list' %}" class="btn btn-outline-info">
            <i class="fas fa-box me-2"></i>تصفح Products
        </a>
        <a href="{% url 'inventory:customer_list' %}" class="btn btn-outline-success">
            <i class="fas fa-users me-2"></i>العميل List
        </a>
    </div>

    <div class="row">
        <!-- Quick Sale Form -->
        <div class="col-lg-8">
            <div class="quick-sale-card">
                <h4 class="mb-4">
                    <i class="fas fa-shopping-cart me-2"></i>تفاصيل البيع
                </h4>

                <form method="post" id="quickSaleForm">
                    {% csrf_token %}
                    
                    <!-- Customer Selection -->
                    <div class="form-section">
                        <h5 class="mb-3">
                            <i class="fas fa-user me-2"></i>العميل Information
                        </h5>
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">العميل *</label>
                                {{ form.customer|add_class:"form-select" }}
                                {% if form.customer.errors %}
                                    <div class="text-danger mt-1">{{ form.customer.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="{% url 'inventory:customer_create' %}" class="btn btn-outline-success" target="_blank">
                                        <i class="fas fa-plus me-1"></i>New Customer
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Selection -->
                    <div class="form-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-box me-2"></i>المنتجات Information
                            </h5>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="addProductBtn">
                                <i class="fas fa-plus me-1"></i>إضافة منتج
                            </button>
                        </div>

                        <!-- Products Container -->
                        <div id="productsContainer">
                            <!-- Initial Product Row -->
                            <div class="product-row border rounded p-3 mb-3" data-row="0">
                                <div class="row">
                                    <div class="col-md-5">
                                        <label class="form-label">المنتج *</label>
                                        {{ form.product|add_class:"form-select product-select" }}
                                        {% if form.product.errors %}
                                            <div class="text-danger mt-1">{{ form.product.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">الكمية *</label>
                                        {{ form.quantity|add_class:"form-control quantity-input" }}
                                        {% if form.quantity.errors %}
                                            <div class="text-danger mt-1">{{ form.quantity.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">سعر الوحدة *</label>
                                        {{ form.unit_price|add_class:"form-control unit-price-input" }}
                                        {% if form.unit_price.errors %}
                                            <div class="text-danger mt-1">{{ form.unit_price.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">الإجمالي</label>
                                        <div class="form-control-plaintext fw-bold item-total">0.00 ج.م</div>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="button" class="btn btn-outline-danger btn-sm remove-product-btn" style="display: none;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Product Information Display -->
                                <div class="product-info-display" style="display: none;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing & Discount -->
                    <div class="form-section">
                        <h5 class="mb-3">
                            <i class="fas fa-percent me-2"></i>Pricing & Discount
                        </h5>
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">الخصم %</label>
                                {{ form.discount_percentage|add_class:"form-control" }}
                                {% if form.discount_percentage.errors %}
                                    <div class="text-danger mt-1">{{ form.discount_percentage.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الدفع Method *</label>
                                {{ form.payment_method|add_class:"form-select" }}
                                {% if form.payment_method.errors %}
                                    <div class="text-danger mt-1">{{ form.payment_method.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-quick-sale" id="completeSaleBtn">
                                        <i class="fas fa-check me-2"></i>Complete Sale
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Profit Information Display -->
                        <div id="profitInfo" style="display: none;"></div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Calculation Display -->
        <div class="col-lg-4">
            <div class="calculation-display">
                <div class="amount-display" id="totalAmount">$0.00</div>
                
                <div class="breakdown-item">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotalAmount">$0.00</span>
                </div>
                
                <div class="breakdown-item">
                    <span>الخصم</span>
                    <span id="discountAmount">$0.00</span>
                </div>
                
                <div class="breakdown-item">
                    <span>الإجمالي:</span>
                    <span id="finalAmount">$0.00</span>
                </div>

                <div class="mt-4">
                    <h6>إحصائيات سريعة</h6>
                    <div class="breakdown-item">
                        <span>الكمية:</span>
                        <span id="displayQuantity">0</span>
                    </div>
                    <div class="breakdown-item">
                        <span>Unit Price:</span>
                        <span id="displayUnitPrice">$0.00</span>
                    </div>
                    <div class="breakdown-item">
                        <span>Profit:</span>
                        <span id="displayProfit">$0.00</span>
                    </div>
                </div>

                <div class="mt-4">
                    <small class="opacity-75">
                        <i class="fas fa-info-circle me-1"></i>
                        Calculation updates automatically
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let productRowCount = 1;
    const productsContainer = document.getElementById('productsContainer');
    const addProductBtn = document.getElementById('addProductBtn');
    const discountInput = document.querySelector('input[name="discount_percentage"]');
    const profitInfoDiv = document.getElementById('profitInfo');

    // Event listeners
    addProductBtn.addEventListener('click', addProductRow);
    discountInput.addEventListener('input', calculateTotals);

    // Initialize first row
    initializeProductRow(document.querySelector('.product-row'));
    
    // Form validation
    document.getElementById('quickSaleForm').addEventListener('submit', validateForm);

    function addProductRow() {
        const newRow = document.createElement('div');
        newRow.className = 'product-row border rounded p-3 mb-3';
        newRow.dataset.row = productRowCount;

        newRow.innerHTML = `
            <div class="row">
                <div class="col-md-5">
                    <label class="form-label">المنتج *</label>
                    <select class="form-select product-select" name="additional_product_${productRowCount}" required>
                        <option value="">اختر منتج</option>
                        ${getProductOptions()}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الكمية *</label>
                    <input type="number" class="form-control quantity-input" name="additional_quantity_${productRowCount}" min="1" value="1" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">سعر الوحدة *</label>
                    <input type="number" class="form-control unit-price-input" name="additional_unit_price_${productRowCount}" step="0.01" min="0.01" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الإجمالي</label>
                    <div class="form-control-plaintext fw-bold item-total">0.00 ج.م</div>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-danger btn-sm remove-product-btn">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="product-info-display" style="display: none;"></div>
        `;

        productsContainer.appendChild(newRow);
        initializeProductRow(newRow);
        productRowCount++;
        updateRemoveButtons();
    }

    function getProductOptions() {
        const originalSelect = document.querySelector('select[name="product"]');
        let options = '';
        for (let i = 1; i < originalSelect.options.length; i++) {
            const option = originalSelect.options[i];
            options += `<option value="${option.value}">${option.text}</option>`;
        }
        return options;
    }

    function updateRemoveButtons() {
        const rows = document.querySelectorAll('.product-row');
        const removeButtons = document.querySelectorAll('.remove-product-btn');

        removeButtons.forEach((btn, index) => {
            if (rows.length > 1) {
                btn.style.display = 'block';
            } else {
                btn.style.display = 'none';
            }
        });
    }

    function initializeProductRow(row) {
        const productSelect = row.querySelector('.product-select');
        const quantityInput = row.querySelector('.quantity-input');
        const unitPriceInput = row.querySelector('.unit-price-input');
        const removeBtn = row.querySelector('.remove-product-btn');

        // Event listeners
        productSelect.addEventListener('change', function() {
            handleProductChange(row);
        });
        quantityInput.addEventListener('input', calculateTotals);
        unitPriceInput.addEventListener('input', calculateTotals);

        if (removeBtn) {
            removeBtn.addEventListener('click', function() {
                row.remove();
                updateRemoveButtons();
                calculateTotals();
            });
        }
    }

    function handleProductChange(row) {
        const productSelect = row.querySelector('.product-select');
        const unitPriceInput = row.querySelector('.unit-price-input');
        const productInfoDiv = row.querySelector('.product-info-display');
        const selectedOption = productSelect.options[productSelect.selectedIndex];

        if (selectedOption.value) {
            // Fetch product price via AJAX
            fetch(`{% url 'sales:get_product_price' %}?product_id=${selectedOption.value}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        unitPriceInput.value = data.price;

                        // Display product information
                        let infoHtml = `
                            <div class="product-info mt-3">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات المنتج</h6>
                                <div class="row">
                                    <div class="col-4">
                                        <strong>المخزون المتاح:</strong><br>
                                        <span class="${data.stock > 0 ? 'text-success' : 'text-danger'}">${data.stock} قطعة</span>
                                    </div>
                                    <div class="col-4">
                                        <strong>سعر البيع:</strong><br>
                                        ${data.price} ج.م
                                    </div>
                                    <div class="col-4">
                                        <strong>سعر التكلفة:</strong><br>
                                        ${data.cost_price} ج.م
                                    </div>
                                </div>
                            </div>
                        `;

                        if (data.stock === 0) {
                            infoHtml += `
                                <div class="stock-warning mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>تحذير:</strong> هذا المنتج غير متوفر في المخزون!
                                </div>
                            `;
                        } else if (data.stock < 5) {
                            infoHtml += `
                                <div class="stock-warning mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>مخزون منخفض:</strong> متبقي ${data.stock} قطع فقط!
                                </div>
                            `;
                        }

                        productInfoDiv.innerHTML = infoHtml;
                        productInfoDiv.style.display = 'block';

                        // Store cost price and stock for calculations
                        productSelect.dataset.costPrice = data.cost_price;
                        productSelect.dataset.stock = data.stock;

                        calculateTotals();
                    }
                })
                .catch(error => {
                    console.error('Error fetching product price:', error);
                });
        } else {
            productInfoDiv.style.display = 'none';
            unitPriceInput.value = '';
            calculateTotals();
        }
    }
    
    function calculateTotals() {
        const productRows = document.querySelectorAll('.product-row');
        let grandSubtotal = 0;
        let totalQuantity = 0;
        let totalProfit = 0;
        let hasValidItems = false;

        // Calculate each row
        productRows.forEach(row => {
            const productSelect = row.querySelector('.product-select');
            const quantityInput = row.querySelector('.quantity-input');
            const unitPriceInput = row.querySelector('.unit-price-input');
            const itemTotalDiv = row.querySelector('.item-total');

            const quantity = parseFloat(quantityInput.value) || 0;
            const unitPrice = parseFloat(unitPriceInput.value) || 0;
            const costPrice = parseFloat(productSelect.dataset.costPrice) || 0;

            const itemSubtotal = quantity * unitPrice;
            const itemProfit = (unitPrice - costPrice) * quantity;

            // Update item total display
            itemTotalDiv.textContent = `${itemSubtotal.toFixed(2)} ج.م`;

            if (productSelect.value && quantity > 0 && unitPrice > 0) {
                grandSubtotal += itemSubtotal;
                totalQuantity += quantity;
                totalProfit += itemProfit;
                hasValidItems = true;
            }
        });

        // Apply discount
        const discountPercentage = parseFloat(discountInput.value) || 0;
        const discountAmount = (grandSubtotal * discountPercentage) / 100;
        const finalTotal = grandSubtotal - discountAmount;

        // Update display
        document.getElementById('subtotalAmount').textContent = `${grandSubtotal.toFixed(2)} ج.م`;
        document.getElementById('discountAmount').textContent = `${discountAmount.toFixed(2)} ج.م`;
        document.getElementById('totalAmount').textContent = `${finalTotal.toFixed(2)} ج.م`;
        document.getElementById('finalAmount').textContent = `${finalTotal.toFixed(2)} ج.م`;
        document.getElementById('displayQuantity').textContent = totalQuantity;
        document.getElementById('displayUnitPrice').textContent = `${(grandSubtotal / totalQuantity || 0).toFixed(2)} ج.م`;
        document.getElementById('displayProfit').textContent = `${totalProfit.toFixed(2)} ج.م`;

        // Update profit information
        if (hasValidItems && totalProfit !== 0) {
            let profitClass = 'profit-indicator';
            let profitIcon = 'fa-arrow-up';

            if (totalProfit < 0) {
                profitClass = 'stock-warning';
                profitIcon = 'fa-arrow-down';
            }

            const profitMargin = grandSubtotal > 0 ? (totalProfit / grandSubtotal * 100) : 0;
            const profitHtml = `
                <div class="${profitClass}">
                    <i class="fas ${profitIcon} me-2"></i>
                    <strong>تحليل الربح:</strong> ${totalProfit.toFixed(2)} ج.م
                    (${profitMargin.toFixed(1)}% هامش)
                </div>
            `;

            profitInfoDiv.innerHTML = profitHtml;
            profitInfoDiv.style.display = 'block';
        } else {
            profitInfoDiv.style.display = 'none';
        }

        // Update button state
        const submitBtn = document.getElementById('completeSaleBtn');
        if (finalTotal > 0 && hasValidItems) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = `<i class="fas fa-check me-2"></i>إتمام البيع - ${finalTotal.toFixed(2)} ج.م`;
        } else {
            submitBtn.disabled = true;
            submitBtn.innerHTML = `<i class="fas fa-check me-2"></i>إتمام البيع`;
        }
    }
    
    function validateForm(e) {
        const customerValue = document.querySelector('select[name="customer"]').value;
        const productRows = document.querySelectorAll('.product-row');
        let hasValidItems = false;
        let totalAmount = 0;

        if (!customerValue) {
            e.preventDefault();
            alert('يرجى اختيار العميل.');
            return false;
        }

        // Validate each product row
        for (let row of productRows) {
            const productSelect = row.querySelector('.product-select');
            const quantityInput = row.querySelector('.quantity-input');
            const unitPriceInput = row.querySelector('.unit-price-input');

            const productValue = productSelect.value;
            const quantity = parseFloat(quantityInput.value) || 0;
            const unitPrice = parseFloat(unitPriceInput.value) || 0;
            const stock = parseInt(productSelect.dataset.stock) || 0;

            if (productValue && quantity > 0 && unitPrice > 0) {
                hasValidItems = true;

                // Check stock availability
                if (quantity > stock) {
                    e.preventDefault();
                    alert(`مخزون غير كافي للمنتج. المتاح: ${stock}، المطلوب: ${quantity}`);
                    return false;
                }

                totalAmount += quantity * unitPrice;
            }
        }

        if (!hasValidItems) {
            e.preventDefault();
            alert('يرجى إضافة منتج واحد على الأقل مع كمية وسعر صحيحين.');
            return false;
        }

        // Apply discount
        const discountPercentage = parseFloat(discountInput.value) || 0;
        const discountAmount = (totalAmount * discountPercentage) / 100;
        const finalTotal = totalAmount - discountAmount;

        // Confirm sale
        if (!confirm(`تأكيد البيع بمبلغ ${finalTotal.toFixed(2)} ج.م؟`)) {
            e.preventDefault();
            return false;
        }

        return true;
    }
    
    // Initialize
    calculateTotals();
});
</script>
{% endblock %}