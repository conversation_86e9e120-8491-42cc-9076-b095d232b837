{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Installment Plans | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .filter-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .installments-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    .installments-table th {
        background: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem;
    }
    .installments-table td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid #f1f3f4;
    }
    .installments-table tbody tr:hover {
        background: #f8f9fa;
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
    }
    .stats-row {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
        overflow: hidden;
    }
    .progress-fill {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
    .overdue-indicator {
        color: #dc3545;
        font-weight: bold;
    }
    .upcoming-indicator {
        color: #ffc107;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="fas fa-calendar-alt me-3"></i>Installment Plans
                </h1>
                <p class="mb-0 opacity-75">Manage customer installment plans and payments</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'sales:sale_list' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>رجوع إلي البيع
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ total_installments }}</div>
                    <div class="stat-label">الإجمالي Plans</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ active_installments }}</div>
                    <div class="stat-label">نشط Plans</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value text-warning">{{ overdue_payments }}</div>
                    <div class="stat-label">متأخر Payments</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value text-success">${{ total_collected|floatformat:2 }}</div>
                    <div class="stat-label">الإجمالي Collected</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">بحث</label>
                <input type="text" name="search" class="form-control" placeholder="Customer name, sale number..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">الكل Statuses</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتمل</option>
                    <option value="defaulted" {% if request.GET.status == 'defaulted' %}selected{% endif %}>Defaulted</option>
                    <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>إلغاءled</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الدفع الحالة</label>
                <select name="payment_status" class="form-select">
                    <option value="">الكل</option>
                    <option value="current" {% if request.GET.payment_status == 'current' %}selected{% endif %}>Current</option>
                    <option value="overdue" {% if request.GET.payment_status == 'overdue' %}selected{% endif %}>متأخر</option>
                    <option value="upcoming" {% if request.GET.payment_status == 'upcoming' %}selected{% endif %}>Upcoming</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Sort By</label>
                <select name="sort" class="form-select">
                    <option value="start_date" {% if request.GET.sort == 'start_date' %}selected{% endif %}>Start التاريخ</option>
                    <option value="total_amount" {% if request.GET.sort == 'total_amount' %}selected{% endif %}>الإجمالي المبلغ</option>
                    <option value="remaining" {% if request.GET.sort == 'remaining' %}selected{% endif %}>Remaining الرصيد</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>تصفية
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{% url 'sales:installment_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Installments Table -->
    {% if installments %}
    <div class="installments-table">
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>رقم البيع</th>
                    <th>العميل</th>
                    <th>الإجمالي المبلغ</th>
                    <th>Down الدفع</th>
                    <th>الأقساط</th>
                    <th>Progress</th>
                    <th>Remaining</th>
                    <th>التالي مستحق</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for installment in installments %}
                <tr>
                    <td>
                        <a href="{% url 'sales:sale_detail' installment.sale.id %}" class="fw-bold text-decoration-none">
                            {{ installment.sale.sale_number }}
                        </a>
                    </td>
                    <td>
                        <div>
                            <div class="fw-bold">{{ installment.sale.customer.name }}</div>
                            <small class="text-muted">{{ installment.sale.customer.phone }}</small>
                        </div>
                    </td>
                    <td>
                        <div class="fw-bold">${{ installment.total_amount|floatformat:2 }}</div>
                    </td>
                    <td>
                        <div class="text-success">${{ installment.down_payment|floatformat:2 }}</div>
                    </td>
                    <td>
                        <div class="fw-bold">{{ installment.paid_installments }}/{{ installment.number_of_installments }}</div>
                        <small class="text-muted">${{ installment.installment_amount|floatformat:2 }} each</small>
                    </td>
                    <td style="width: 120px;">
                        {% with progress_percent=installment.payment_progress %}
                        <div class="progress-bar-custom">
                            <div class="progress-fill 
                                {% if progress_percent == 100 %}bg-success
                                {% elif progress_percent >= 75 %}bg-info
                                {% elif progress_percent >= 50 %}bg-warning
                                {% else %}bg-danger{% endif %}" 
                                style="width: {{ progress_percent }}%"></div>
                        </div>
                        <small class="text-muted">{{ progress_percent }}%</small>
                        {% endwith %}
                    </td>
                    <td>
                        {% if installment.remaining_balance > 0 %}
                            <div class="text-danger fw-bold">${{ installment.remaining_balance|floatformat:2 }}</div>
                        {% else %}
                            <div class="text-success fw-bold">مدفوع</div>
                        {% endif %}
                    </td>
                    <td>
                        {% with next_payment=installment.next_due_payment %}
                        {% if next_payment %}
                            <div>{{ next_payment.due_date|date:"M d, Y" }}</div>
                            {% if next_payment.is_overdue %}
                                <small class="overdue-indicator">{{ next_payment.days_overdue }} days overdue</small>
                            {% elif next_payment.days_until_due <= 7 %}
                                <small class="upcoming-indicator">مستحق in {{ next_payment.days_until_due }} days</small>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                        {% endwith %}
                    </td>
                    <td>
                        <span class="badge status-badge 
                            {% if installment.status == 'active' %}bg-success
                            {% elif installment.status == 'completed' %}bg-primary
                            {% elif installment.status == 'defaulted' %}bg-danger
                            {% elif installment.status == 'cancelled' %}bg-secondary
                            {% else %}bg-warning{% endif %}">
                            {{ installment.get_status_display }}
                        </span>
                    </td>
                    <td>
                        <div class="dropdown">
                            <button class="btn btn-link btn-sm" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'sales:installment_detail' installment.id %}">
                                    <i class="fas fa-eye me-2"></i>عرض Details</a></li>
                                <li><a class="dropdown-item" href="{% url 'sales:sale_detail' installment.sale.id %}">
                                    <i class="fas fa-file-invoice me-2"></i>عرض Sale</a></li>
                                {% with next_payment=installment.next_due_payment %}
                                {% if next_payment and next_payment.status != 'paid' %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'sales:installment_payment' next_payment.id %}">
                                    <i class="fas fa-credit-card me-2"></i>Record الدفع</a></li>
                                {% endif %}
                                {% endwith %}
                            </ul>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Installment pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="fas fa-calendar-alt fa-5x text-muted mb-4"></i>
        <h3 class="text-muted mb-3">No Installment Plans Found</h3>
        <p class="text-muted mb-4">Start creating sales with installment payment options.</p>
        <a href="{% url 'sales:sale_create' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>إنشاء البيع بالتقسيط
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name="status"], select[name="payment_status"], select[name="sort"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}