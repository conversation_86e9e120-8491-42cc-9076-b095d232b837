{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إضافة دفعة للشراء | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .payment-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .payment-card .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
        border-radius: 15px 15px 0 0;
    }
    .purchase-summary {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        position: sticky;
        top: 2rem;
    }
    .amount-display {
        font-size: 2.5rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    .summary-row {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .summary-row:last-child {
        border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        font-weight: bold;
    }
    .payment-method-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .payment-method-card:hover {
        border-color: #28a745;
        background-color: #f8fff9;
    }
    .payment-method-card.selected {
        border-color: #28a745;
        background-color: #f8fff9;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    .payment-method-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #28a745;
    }
    .quick-amount-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .quick-amount-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
    }
    .payment-amount-input {
        font-size: 1.5rem;
        font-weight: bold;
        text-align: center;
        border: 2px solid #28a745;
        border-radius: 10px;
        padding: 1rem;
    }
    .payment-amount-input:focus {
        border-color: #20c997;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    .btn-submit-payment {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        font-size: 1.2rem;
        font-weight: bold;
        padding: 1rem 2rem;
        border-radius: 10px;
        width: 100%;
        transition: all 0.3s ease;
    }
    .btn-submit-payment:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        color: white;
    }
    .form-floating > label {
        color: #6c757d;
    }
    .conditional-field {
        display: none;
        margin-top: 1rem;
    }
    .conditional-field.show {
        display: block;
        animation: fadeIn 0.3s ease;
    }
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .supplier-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .supplier-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #28a745;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="fas fa-credit-card me-3"></i>إضافة دفعة للشراء
                </h1>
                <p class="mb-0 opacity-75">تسجيل دفعة جديدة لأمر الشراء {{ purchase.purchase_number }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'purchases:purchase_detail' purchase.id %}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>رجوع للتفاصيل
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Payment Form -->
        <div class="col-lg-8">
            <!-- Supplier Information -->
            <div class="supplier-info">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="supplier-avatar">
                            {{ purchase.supplier.name|slice:":2"|upper }}
                        </div>
                    </div>
                    <div class="col">
                        <h5 class="mb-1">{{ purchase.supplier.name }}</h5>
                        <p class="mb-0 text-muted">
                            <i class="fas fa-envelope me-2"></i>{{ purchase.supplier.email|default:"لا يوجد بريد إلكتروني" }}
                            {% if purchase.supplier.phone %}
                            <span class="ms-3"><i class="fas fa-phone me-2"></i>{{ purchase.supplier.phone }}</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <form method="post" id="paymentForm">
                {% csrf_token %}
                
                <!-- Payment Amount Section -->
                <div class="payment-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>مبلغ الدفعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">مبلغ الدفعة *</label>
                                {{ form.amount|add_class:"form-control payment-amount-input" }}
                                {% if form.amount.errors %}
                                    <div class="text-danger mt-1">{{ form.amount.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">الحد الأقصى: {{ purchase.balance_amount|floatformat:2 }} ج.م</small>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">تاريخ الدفعة *</label>
                                {{ form.payment_date|add_class:"form-control" }}
                                {% if form.payment_date.errors %}
                                    <div class="text-danger mt-1">{{ form.payment_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Method Section -->
                <div class="payment-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>طريقة الدفع</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                {{ form.payment_method|add_class:"form-select" }}
                                {% if form.payment_method.errors %}
                                    <div class="text-danger mt-1">{{ form.payment_method.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Reference Number -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">رقم المرجع</label>
                                {{ form.reference_number|add_class:"form-control" }}
                                {% if form.reference_number.errors %}
                                    <div class="text-danger mt-1">{{ form.reference_number.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">رقم الشيك، رقم التحويل، إلخ</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="payment-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h5>
                    </div>
                    <div class="card-body">
                        {{ form.notes|add_class:"form-control" }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" class="btn btn-submit-payment">
                        <i class="fas fa-check me-2"></i>تسجيل الدفعة
                    </button>
                </div>
            </form>
        </div>

        <!-- Purchase Summary Sidebar -->
        <div class="col-lg-4">
            <div class="purchase-summary">
                <div class="text-center mb-4">
                    <h4 class="mb-2">{{ purchase.purchase_number }}</h4>
                    <div class="amount-display">{{ purchase.balance_amount|floatformat:2 }} ج.م</div>
                    <p class="mb-0">الرصيد المستحق</p>
                </div>

                <div class="summary-details">
                    <div class="summary-row">
                        <span>إجمالي الشراء:</span>
                        <span>{{ purchase.total_amount|floatformat:2 }} ج.م</span>
                    </div>
                    <div class="summary-row">
                        <span>المدفوع:</span>
                        <span>{{ purchase.paid_amount|floatformat:2 }} ج.م</span>
                    </div>
                    <div class="summary-row">
                        <span>الرصيد المستحق:</span>
                        <span class="text-warning fw-bold">{{ purchase.balance_amount|floatformat:2 }} ج.م</span>
                    </div>
                </div>

                <!-- Quick Amount Buttons -->
                <div class="text-center mt-4">
                    <h6 class="mb-3">مبالغ سريعة</h6>
                    <button type="button" class="quick-amount-btn" data-amount="{{ purchase.balance_amount }}">
                        كامل المبلغ
                    </button>
                    <button type="button" class="quick-amount-btn" data-amount="half">
                        نصف المبلغ
                    </button>
                    <button type="button" class="quick-amount-btn" data-amount="quarter">
                        ربع المبلغ
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.querySelector('input[name="amount"]');
    const paymentMethodSelect = document.querySelector('select[name="payment_method"]');
    const quickAmountButtons = document.querySelectorAll('.quick-amount-btn');
    
    const currentBalance = {{ purchase.balance_amount }};

    // Quick amount buttons
    quickAmountButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amountType = this.getAttribute('data-amount');
            let amount;

            if (amountType === 'half') {
                amount = currentBalance / 2;
            } else if (amountType === 'quarter') {
                amount = currentBalance / 4;
            } else {
                amount = parseFloat(amountType);
            }

            amountInput.value = amount.toFixed(2);
            validateAmount();
        });
    });

    // Amount validation
    function validateAmount() {
        const amount = parseFloat(amountInput.value) || 0;
        const submitButton = document.querySelector('.btn-submit-payment');
        
        if (amount > currentBalance) {
            amountInput.classList.add('is-invalid');
            submitButton.disabled = true;
            showError('المبلغ لا يمكن أن يكون أكبر من الرصيد المستحق');
        } else if (amount <= 0) {
            amountInput.classList.add('is-invalid');
            submitButton.disabled = true;
            showError('المبلغ يجب أن يكون أكبر من صفر');
        } else {
            amountInput.classList.remove('is-invalid');
            amountInput.classList.add('is-valid');
            submitButton.disabled = false;
            hideError();
        }
    }

    function showError(message) {
        let errorDiv = document.querySelector('.amount-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'text-danger mt-1 amount-error';
            amountInput.parentNode.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }

    function hideError() {
        const errorDiv = document.querySelector('.amount-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // Real-time amount validation
    amountInput.addEventListener('input', validateAmount);

    // Form submission validation
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value) || 0;
        
        if (amount <= 0 || amount > currentBalance) {
            e.preventDefault();
            validateAmount();
            alert('يرجى إدخال مبلغ صحيح');
            return false;
        }
        
        // Confirm submission
        if (!confirm(`هل أنت متأكد من تسجيل دفعة بمبلغ ${amount.toFixed(2)} ج.م؟`)) {
            e.preventDefault();
            return false;
        }
    });

    // Initialize validation
    validateAmount();
});
</script>
{% endblock %}
