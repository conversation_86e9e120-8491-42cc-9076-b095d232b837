# Generated by Django 4.2.7 on 2025-09-16 13:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0005_finalize_unit_migration'),
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('invoice_type', models.CharField(choices=[('sale', 'فاتورة بيع'), ('purchase', 'فاتورة شراء')], max_length=20, verbose_name='نوع الفاتورة')),
                ('invoice_date', models.DateField(verbose_name='تاريخ الفاتورة')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='المجموع الفرعي')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, verbose_name='نسبة الخصم')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='مبلغ الخصم')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, verbose_name='نسبة الضريبة')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='المبلغ المدفوع')),
                ('remaining_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='المبلغ المتبقي')),
                ('payment_method', models.CharField(choices=[('cash', 'نقداً'), ('credit', 'آجل'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('card', 'بطاقة ائتمان')], default='cash', max_length=20, verbose_name='طريقة الدفع')),
                ('payment_reference', models.CharField(blank=True, max_length=100, verbose_name='مرجع الدفع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('confirmed', 'مؤكدة'), ('paid', 'مدفوعة'), ('partially_paid', 'مدفوعة جزئياً'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('internal_notes', models.TextField(blank=True, verbose_name='ملاحظات داخلية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_invoices', to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='inventory.customer', verbose_name='العميل')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='inventory.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'db_table': 'invoices',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ShopSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shop_name', models.CharField(max_length=200, verbose_name='اسم المحل')),
                ('shop_name_english', models.CharField(blank=True, max_length=200, verbose_name='اسم المحل بالإنجليزية')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='shop/', verbose_name='شعار المحل')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, verbose_name='رقم الموبايل')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='المدينة')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='المحافظة')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='الرمز البريدي')),
                ('country', models.CharField(default='مصر', max_length=100, verbose_name='البلد')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('commercial_register', models.CharField(blank=True, max_length=50, verbose_name='السجل التجاري')),
                ('license_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الترخيص')),
                ('invoice_prefix', models.CharField(default='INV', max_length=10, verbose_name='بادئة رقم الفاتورة')),
                ('invoice_footer', models.TextField(blank=True, verbose_name='تذييل الفاتورة')),
                ('terms_and_conditions', models.TextField(blank=True, verbose_name='الشروط والأحكام')),
                ('currency_symbol', models.CharField(default='ج.م', max_length=10, verbose_name='رمز العملة')),
                ('currency_name', models.CharField(default='جنيه مصري', max_length=50, verbose_name='اسم العملة')),
                ('date_format', models.CharField(default='%Y/%m/%d', max_length=20, verbose_name='تنسيق التاريخ')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'إعدادات المحل',
                'verbose_name_plural': 'إعدادات المحل',
                'db_table': 'shop_settings',
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('product_sku', models.CharField(max_length=100, verbose_name='رمز المنتج')),
                ('unit_name', models.CharField(max_length=50, verbose_name='الوحدة')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, verbose_name='نسبة الخصم')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='مبلغ الخصم')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='السعر الإجمالي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.invoice', verbose_name='الفاتورة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر فاتورة',
                'verbose_name_plural': 'عناصر الفاتورة',
                'db_table': 'invoice_items',
                'ordering': ['id'],
            },
        ),
    ]
