# Generated by Django 4.2.7 on 2025-09-14 12:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('theme', models.CharField(choices=[('light', 'Light Theme'), ('dark', 'Dark Theme'), ('auto', 'Auto (System)')], default='light', max_length=10)),
                ('language', models.CharField(choices=[('en', 'English'), ('ar', 'Arabic')], default='en', max_length=5)),
                ('timezone', models.Char<PERSON>ield(default='UTC', max_length=50)),
                ('date_format', models.CharField(default='Y-m-d', max_length=20)),
                ('time_format', models.CharField(default='H:i:s', max_length=20)),
                ('dashboard_layout', models.JSONField(blank=True, default=dict)),
                ('default_page', models.CharField(default='/dashboard/', max_length=100)),
                ('items_per_page', models.IntegerField(default=20)),
                ('email_notifications', models.BooleanField(default=True)),
                ('push_notifications', models.BooleanField(default=True)),
                ('notification_sound', models.BooleanField(default=True)),
                ('default_report_format', models.CharField(default='pdf', max_length=10)),
                ('auto_download_reports', models.BooleanField(default=False)),
                ('advanced_mode', models.BooleanField(default=False)),
                ('show_tooltips', models.BooleanField(default=True)),
                ('auto_save', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Preference',
                'verbose_name_plural': 'User Preferences',
                'db_table': 'user_preferences',
            },
        ),
        migrations.CreateModel(
            name='SystemAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('alert_type', models.CharField(choices=[('maintenance', 'Maintenance'), ('outage', 'Service Outage'), ('update', 'System Update'), ('security', 'Security Alert'), ('announcement', 'Announcement'), ('promotion', 'Promotion')], max_length=20)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('expired', 'Expired'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('show_on_dashboard', models.BooleanField(default=True)),
                ('show_on_login', models.BooleanField(default=False)),
                ('is_dismissible', models.BooleanField(default=True)),
                ('background_color', models.CharField(default='#f8d7da', max_length=7)),
                ('text_color', models.CharField(default='#721c24', max_length=7)),
                ('target_roles', models.JSONField(blank=True, default=list)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Alert',
                'verbose_name_plural': 'System Alerts',
                'db_table': 'system_alerts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(choices=[('general', 'General'), ('business', 'Business'), ('financial', 'Financial'), ('inventory', 'Inventory'), ('email', 'Email'), ('security', 'Security'), ('integration', 'Integration')], max_length=20)),
                ('key', models.CharField(max_length=100)),
                ('value', models.TextField()),
                ('data_type', models.CharField(default='string', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('is_required', models.BooleanField(default=False)),
                ('validation_regex', models.CharField(blank=True, max_length=500)),
                ('min_value', models.FloatField(blank=True, null=True)),
                ('max_value', models.FloatField(blank=True, null=True)),
                ('is_sensitive', models.BooleanField(default=False)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Configuration',
                'verbose_name_plural': 'System Configuration',
                'db_table': 'system_configuration',
                'ordering': ['category', 'key'],
                'unique_together': {('category', 'key')},
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('info', 'Information'), ('warning', 'Warning'), ('error', 'Error'), ('success', 'Success'), ('alert', 'Alert'), ('reminder', 'Reminder')], default='info', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20)),
                ('is_read', models.BooleanField(default=False)),
                ('is_archived', models.BooleanField(default=False)),
                ('action_url', models.CharField(blank=True, max_length=500)),
                ('action_text', models.CharField(blank=True, max_length=100)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'db_table': 'notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_read'], name='notificatio_user_id_a4dd5c_idx'), models.Index(fields=['created_at'], name='notificatio_created_e4c995_idx'), models.Index(fields=['expires_at'], name='notificatio_expires_66996e_idx')],
            },
        ),
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'Create'), ('read', 'Read'), ('update', 'Update'), ('delete', 'Delete'), ('login', 'Login'), ('logout', 'Logout'), ('export', 'Export'), ('import', 'Import'), ('approve', 'Approve'), ('reject', 'Reject'), ('cancel', 'Cancel')], max_length=20)),
                ('description', models.CharField(max_length=500)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('additional_data', models.JSONField(blank=True, default=dict)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Activity Log',
                'verbose_name_plural': 'Activity Logs',
                'db_table': 'activity_logs',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='activity_lo_user_id_e40ffe_idx'), models.Index(fields=['action', 'timestamp'], name='activity_lo_action_fe3d88_idx'), models.Index(fields=['content_type', 'object_id'], name='activity_lo_content_edc5bf_idx')],
            },
        ),
    ]
