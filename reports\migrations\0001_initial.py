# Generated by Django 4.2.7 on 2025-09-14 12:21

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, max_length=50)),
                ('color', models.CharField(default='#007bff', max_length=7)),
                ('sort_order', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Report Category',
                'verbose_name_plural': 'Report Categories',
                'db_table': 'report_categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('report_type', models.CharField(choices=[('sales', 'Sales Report'), ('purchases', 'Purchase Report'), ('inventory', 'Inventory Report'), ('expenses', 'Expense Report'), ('profit_loss', 'Profit & Loss Report'), ('customer_statement', 'Customer Statement'), ('supplier_statement', 'Supplier Statement'), ('aging', 'Aging Report'), ('stock_movement', 'Stock Movement Report'), ('low_stock', 'Low Stock Report'), ('installment', 'Installment Report'), ('daily_summary', 'Daily Summary'), ('weekly_summary', 'Weekly Summary'), ('monthly_summary', 'Monthly Summary'), ('custom', 'Custom Report')], max_length=30)),
                ('parameters', models.JSONField(blank=True, default=dict)),
                ('filters', models.JSONField(blank=True, default=dict)),
                ('columns', models.JSONField(blank=True, default=list)),
                ('is_scheduled', models.BooleanField(default=False)),
                ('frequency', models.CharField(choices=[('once', 'One Time'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], default='once', max_length=20)),
                ('schedule_time', models.TimeField(blank=True, null=True)),
                ('next_run_date', models.DateTimeField(blank=True, null=True)),
                ('last_run_date', models.DateTimeField(blank=True, null=True)),
                ('output_format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('html', 'HTML')], default='pdf', max_length=10)),
                ('auto_email', models.BooleanField(default=False)),
                ('email_recipients', models.TextField(blank=True, help_text='Comma-separated email addresses')),
                ('email_subject', models.CharField(blank=True, max_length=200)),
                ('email_body', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_report_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Report Template',
                'verbose_name_plural': 'Report Templates',
                'db_table': 'report_templates',
                'ordering': ['report_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('sales', 'Sales Report'), ('purchases', 'Purchase Report'), ('inventory', 'Inventory Report'), ('expenses', 'Expense Report'), ('profit_loss', 'Profit & Loss Report'), ('customer_statement', 'Customer Statement'), ('supplier_statement', 'Supplier Statement'), ('aging', 'Aging Report'), ('stock_movement', 'Stock Movement Report'), ('low_stock', 'Low Stock Report'), ('installment', 'Installment Report'), ('daily_summary', 'Daily Summary'), ('weekly_summary', 'Weekly Summary'), ('monthly_summary', 'Monthly Summary'), ('custom', 'Custom Report')], max_length=30)),
                ('user_role', models.CharField(max_length=20)),
                ('can_view', models.BooleanField(default=True)),
                ('can_generate', models.BooleanField(default=True)),
                ('can_schedule', models.BooleanField(default=False)),
                ('can_export', models.BooleanField(default=True)),
                ('can_email', models.BooleanField(default=False)),
                ('data_filters', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Report Access',
                'verbose_name_plural': 'Report Access',
                'db_table': 'report_access',
                'ordering': ['report_type', 'user_role'],
                'unique_together': {('report_type', 'user_role')},
            },
        ),
        migrations.CreateModel(
            name='GeneratedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_name', models.CharField(max_length=200)),
                ('report_type', models.CharField(choices=[('sales', 'Sales Report'), ('purchases', 'Purchase Report'), ('inventory', 'Inventory Report'), ('expenses', 'Expense Report'), ('profit_loss', 'Profit & Loss Report'), ('customer_statement', 'Customer Statement'), ('supplier_statement', 'Supplier Statement'), ('aging', 'Aging Report'), ('stock_movement', 'Stock Movement Report'), ('low_stock', 'Low Stock Report'), ('installment', 'Installment Report'), ('daily_summary', 'Daily Summary'), ('weekly_summary', 'Weekly Summary'), ('monthly_summary', 'Monthly Summary'), ('custom', 'Custom Report')], max_length=30)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('generating', 'Generating'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('parameters', models.JSONField(blank=True, default=dict)),
                ('filters', models.JSONField(blank=True, default=dict)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('file_size', models.BigIntegerField(default=0)),
                ('file_format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('html', 'HTML')], default='pdf', max_length=10)),
                ('date_from', models.DateField(blank=True, null=True)),
                ('date_to', models.DateField(blank=True, null=True)),
                ('total_records', models.IntegerField(default=0)),
                ('generation_time', models.FloatField(default=0.0)),
                ('error_message', models.TextField(blank=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_at', models.DateTimeField(blank=True, null=True)),
                ('email_recipients', models.TextField(blank=True)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('downloaded_at', models.DateTimeField(blank=True, null=True)),
                ('download_count', models.IntegerField(default=0)),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='generated_reports', to='reports.reporttemplate')),
            ],
            options={
                'verbose_name': 'Generated Report',
                'verbose_name_plural': 'Generated Reports',
                'db_table': 'generated_reports',
                'ordering': ['-generated_at'],
            },
        ),
        migrations.CreateModel(
            name='DashboardWidget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('widget_type', models.CharField(choices=[('chart', 'Chart'), ('kpi', 'KPI Card'), ('table', 'Data Table'), ('gauge', 'Gauge'), ('progress', 'Progress Bar'), ('alert', 'Alert Box')], max_length=20)),
                ('chart_type', models.CharField(blank=True, choices=[('line', 'Line Chart'), ('bar', 'Bar Chart'), ('pie', 'Pie Chart'), ('doughnut', 'Doughnut Chart'), ('area', 'Area Chart'), ('scatter', 'Scatter Plot')], max_length=20)),
                ('data_source', models.CharField(max_length=100)),
                ('data_query', models.JSONField(blank=True, default=dict)),
                ('refresh_interval', models.IntegerField(default=300)),
                ('size', models.CharField(choices=[('small', 'Small (1x1)'), ('medium', 'Medium (2x1)'), ('large', 'Large (2x2)'), ('wide', 'Wide (3x1)')], default='medium', max_length=20)),
                ('position_x', models.IntegerField(default=0)),
                ('position_y', models.IntegerField(default=0)),
                ('background_color', models.CharField(default='#ffffff', max_length=7)),
                ('text_color', models.CharField(default='#333333', max_length=7)),
                ('allowed_roles', models.JSONField(blank=True, default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Dashboard Widget',
                'verbose_name_plural': 'Dashboard Widgets',
                'db_table': 'dashboard_widgets',
                'ordering': ['position_y', 'position_x'],
            },
        ),
        migrations.CreateModel(
            name='ReportSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('active', 'Active'), ('paused', 'Paused'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('custom_parameters', models.JSONField(blank=True, default=dict)),
                ('custom_filters', models.JSONField(blank=True, default=dict)),
                ('email_address', models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('send_email', models.BooleanField(default=True)),
                ('email_format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('html', 'HTML')], default='pdf', max_length=10)),
                ('subscribed_at', models.DateTimeField(auto_now_add=True)),
                ('last_sent_at', models.DateTimeField(blank=True, null=True)),
                ('total_reports_sent', models.IntegerField(default=0)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='reports.reporttemplate')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_subscriptions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Report Subscription',
                'verbose_name_plural': 'Report Subscriptions',
                'db_table': 'report_subscriptions',
                'ordering': ['-subscribed_at'],
                'unique_together': {('user', 'template')},
            },
        ),
        migrations.CreateModel(
            name='AnalyticsEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('page_view', 'Page View'), ('report_generated', 'Report Generated'), ('report_downloaded', 'Report Downloaded'), ('search', 'Search'), ('filter_applied', 'Filter Applied'), ('export', 'Data Export'), ('login', 'User Login'), ('logout', 'User Logout'), ('error', 'System Error')], max_length=30)),
                ('page_url', models.CharField(blank=True, max_length=500)),
                ('event_data', models.JSONField(blank=True, default=dict)),
                ('user_agent', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('duration', models.FloatField(blank=True, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='analytics_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Analytics Event',
                'verbose_name_plural': 'Analytics Events',
                'db_table': 'analytics_events',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'event_type'], name='analytics_e_user_id_f87fa1_idx'), models.Index(fields=['timestamp'], name='analytics_e_timesta_47536d_idx'), models.Index(fields=['event_type', 'timestamp'], name='analytics_e_event_t_5b2eb4_idx')],
            },
        ),
    ]
