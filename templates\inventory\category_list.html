{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Categories | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .category-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease-in-out;
        margin-bottom: 1.5rem;
    }
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    .category-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem;
        border-radius: 10px 10px 0 0;
    }
    .vehicle-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
    }
    .product-count {
        font-size: 1.5rem;
        font-weight: bold;
        color: #667eea;
    }
    .filter-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .stats-row {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="fas fa-tags me-3"></i>فئات المنتجات
                </h1>
                <p class="mb-0 opacity-75">إدارة فئات المنتجات وأنواع المركبات</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'inventory:category_create' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>إضافة فئة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ total_categories }}</div>
                    <div class="stat-label">إجمالي الفئات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ motorcycle_categories }}</div>
                    <div class="stat-label">دراجة نارية</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ car_categories }}</div>
                    <div class="stat-label">سيارة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ tuktuk_categories }}</div>
                    <div class="stat-label">توكتوك</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">بحث عن فئة معينة</label>
                <input type="text" class="form-control" name="search" 
                       value="{{ request.GET.search }}" placeholder="بحث بالإسم .....">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع المركبة</label>
                <select class="form-select" name="vehicle_type">
                    <option value="">كل انواع المركبات</option>
                    <option value="motorcycle" {% if request.GET.vehicle_type == 'motorcycle' %}selected{% endif %}>دراجية نارية</option>
                    <option value="car" {% if request.GET.vehicle_type == 'car' %}selected{% endif %}>سيارة</option>
                    <option value="tuktuk" {% if request.GET.vehicle_type == 'tuktuk' %}selected{% endif %}>توكتوك</option>
                    <option value="general" {% if request.GET.vehicle_type == 'general' %}selected{% endif %}>عاك</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">تصفية البيانات بواسطة</label>
                <select class="form-select" name="sort">
                    <option value="name" {% if request.GET.sort == 'name' %}selected{% endif %}>الاسم</option>
                    <option value="vehicle_type" {% if request.GET.sort == 'vehicle_type' %}selected{% endif %}>نوع المركبة</option>
                    <option value="product_count" {% if request.GET.sort == 'product_count' %}selected{% endif %}>عدد المنتجات</option>
                    <option value="created_at" {% if request.GET.sort == 'created_at' %}selected{% endif %}>تاريخ الانشاء</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>تصفية
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Categories Grid -->
    {% if categories %}
    <div class="row">
        {% for category in categories %}
        <div class="col-lg-4 col-md-6">
            <div class="card category-card">
                <div class="category-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h5 class="mb-1">{{ category.name }}</h5>
                            <span class="badge vehicle-badge 
                                {% if category.vehicle_type == 'motorcycle' %}bg-primary
                                {% elif category.vehicle_type == 'car' %}bg-success
                                {% elif category.vehicle_type == 'tuktuk' %}bg-warning
                                {% else %}bg-info{% endif %}">
                                {{ category.get_vehicle_type_display }}
                            </span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link btn-sm" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'inventory:category_update' category.id %}"><i class="fas fa-edit me-2"></i>تعديل</a></li>
                                <li><a class="dropdown-item" href="{% url 'inventory:product_list' %}?category={{ category.id }}"><i class="fas fa-eye me-2"></i>عرض المنتجات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete({{ category.id }}, '{{ category.name|escapejs }}')"><i class="fas fa-trash me-2"></i>حذف</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">{{ category.description|default:"No description available"|truncatewords:15 }}</p>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="product-count">{{ category.product_count|default:0 }}</div>
                            <small class="text-muted">المنتج</small>
                        </div>
                        <div class="col-6">
                            <div class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>
                                {{ category.created_at|date:"M d, Y" }}
                            </div>
                            <small class="text-muted">إنشاء</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="row g-2">
                        <div class="col-6">
                            <a href="{% url 'inventory:category_update' category.id %}" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{% url 'inventory:product_list' %}?category={{ category.id }}" class="btn btn-outline-success btn-sm w-100">
                                <i class="fas fa-box me-1"></i>المنتجات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Categories pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.vehicle_type %}&vehicle_type={{ request.GET.vehicle_type }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.vehicle_type %}&vehicle_type={{ request.GET.vehicle_type }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.vehicle_type %}&vehicle_type={{ request.GET.vehicle_type }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.vehicle_type %}&vehicle_type={{ request.GET.vehicle_type }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.vehicle_type %}&vehicle_type={{ request.GET.vehicle_type }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="fas fa-tags fa-5x text-muted mb-4"></i>
        <h3 class="text-muted mb-3">لم يتم العثور على فئات</h3>
        <p class="text-muted mb-4">ابدأ بإنشاء فئة المنتج الأولى الخاصة بك.</p>
        <a href="{% url 'inventory:category_create' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>إنشاء اول فئة
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                </div>
                <h6 class="mb-3">هل أنت متأكد من حذف هذه الفئة؟</h6>
                <p class="text-muted mb-3">
                    <strong id="categoryName"></strong>
                </p>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>حذف نهائياً
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name="vehicle_type"], select[name="sort"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});

// Global function for delete confirmation
function confirmDelete(categoryId, categoryName) {
    // Set category name in modal
    document.getElementById('categoryName').textContent = categoryName;

    // Set up delete action
    document.getElementById('confirmDeleteBtn').onclick = function() {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/inventory/categories/${categoryId}/delete/`;

        // Add CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    };

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}