# Generated by Django 4.2.7 on 2025-09-14 12:21

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sale_number', models.CharField(max_length=50, unique=True)),
                ('sale_type', models.CharField(choices=[('cash', 'Cash Sale'), ('credit', 'Credit Sale'), ('installment', 'Installment Sale'), ('wholesale', 'Wholesale')], default='cash', max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded'), ('partial_refund', 'Partial Refund')], default='pending', max_length=20)),
                ('payment_status', models.CharField(choices=[('paid', 'Paid'), ('partial', 'Partially Paid'), ('unpaid', 'Unpaid'), ('overdue', 'Overdue')], default='unpaid', max_length=20)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('balance_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('sale_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('due_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('internal_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_sales', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales', to='inventory.customer')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='updated_sales', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sale',
                'verbose_name_plural': 'Sales',
                'db_table': 'sales',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cost_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.sale')),
            ],
            options={
                'verbose_name': 'Sale Item',
                'verbose_name_plural': 'Sale Items',
                'db_table': 'sale_items',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_number', models.CharField(max_length=50, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('card', 'Credit/Debit Card'), ('bank_transfer', 'Bank Transfer'), ('check', 'Check'), ('mobile_payment', 'Mobile Payment'), ('credit', 'Store Credit')], max_length=20)),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='completed', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('bank_name', models.CharField(blank=True, max_length=100)),
                ('check_number', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('received_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='sales.sale')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'db_table': 'payments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Installment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('down_payment', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('installment_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('number_of_installments', models.IntegerField()),
                ('interest_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('defaulted', 'Defaulted'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sale', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='installment_plan', to='sales.sale')),
            ],
            options={
                'verbose_name': 'Installment Plan',
                'verbose_name_plural': 'Installment Plans',
                'db_table': 'installments',
            },
        ),
        migrations.CreateModel(
            name='InstallmentPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('installment_number', models.IntegerField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('due_date', models.DateField()),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('paid_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('partial', 'Partially Paid'), ('skipped', 'Skipped')], default='pending', max_length=20)),
                ('late_fee', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('installment_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='installment_payments', to='sales.installment')),
                ('received_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Installment Payment',
                'verbose_name_plural': 'Installment Payments',
                'db_table': 'installment_payments',
                'ordering': ['installment_number'],
                'unique_together': {('installment_plan', 'installment_number')},
            },
        ),
    ]
