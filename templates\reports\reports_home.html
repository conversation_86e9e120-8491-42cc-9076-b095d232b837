

{% load widget_tweaks %}

{% block title %}Reports Management - SpareSmart{% endblock %}
{% block page_title %}Reports Management{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .report-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        border: none;
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
    }
    .report-card .card-header {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        border-radius: 10px 10px 0 0 !important;
        padding: 1.5rem;
    }
    .report-card .card-body {
        padding: 1.5rem;
    }
    .report-icon {
        font-size: 3rem;
        color: #ddd;
        margin-bottom: 1rem;
    }
    .quick-reports {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .report-btn {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        text-decoration: none;
        color: #495057;
        display: block;
        transition: all 0.3s ease;
        height: 100%;
    }
    .report-btn:hover {
        border-color: #007bff;
        color: #007bff;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
    }
    .report-btn i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: block;
    }
    .stats-summary {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #00b894;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="fas fa-chart-bar me-3"></i>إدارة التقارير
                </h1>
                <p class="mb-0 opacity-75">إنشاء تقارير وتحليلات شاملة للأعمال</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'reports:generate_report' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>Generate Report
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Summary -->
    <div class="stats-summary">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ total_reports|default:0 }}</div>
                    <div class="stat-label">الإجمالي التقارير</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ monthly_reports|default:0 }}</div>
                    <div class="stat-label">هذا الشهر</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ report_templates|default:0 }}</div>
                    <div class="stat-label">القوالب</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ scheduled_reports|default:0 }}</div>
                    <div class="stat-label">مجدولة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Report Generation -->
    <div class="quick-reports">
        <h5 class="mb-4">
            <i class="fas fa-bolt me-2"></i>تقرير سريع
        </h5>
        <div class="row g-3">
            <div class="col-md-4">
                <a href="{% url 'reports:sales_report' %}" class="report-btn text-center">
                    <i class="fas fa-shopping-cart text-primary"></i>
                    <h6 class="fw-bold">تقرير المبيعات</h6>
                    <small>Revenue, transactions, and customer analytics</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="{% url 'reports:purchases_report' %}" class="report-btn text-center">
                    <i class="fas fa-truck text-success"></i>
                    <h6 class="fw-bold">Purchase Report</h6>
                    <small>Purchase orders, supplier payments, and costs</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="{% url 'reports:inventory_report' %}" class="report-btn text-center">
                    <i class="fas fa-boxes text-warning"></i>
                    <h6 class="fw-bold">تقرير المخزون</h6>
                    <small>المخزون levels, movements, and valuations</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="{% url 'reports:expenses_report' %}" class="report-btn text-center">
                    <i class="fas fa-receipt text-danger"></i>
                    <h6 class="fw-bold">Expense Report</h6>
                    <small>Business expenses and cost analysis</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="{% url 'reports:profit_loss_report' %}" class="report-btn text-center">
                    <i class="fas fa-chart-line text-info"></i>
                    <h6 class="fw-bold">Profit & Loss</h6>
                    <small>Financial performance and profitability</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="{% url 'reports:installments_report' %}" class="report-btn text-center">
                    <i class="fas fa-calendar-alt text-secondary"></i>
                    <h6 class="fw-bold">الأقساط Report</h6>
                    <small>الدفع schedules and collections</small>
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Report Templates -->
        <div class="col-md-6">
            <div class="report-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Report Templates
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">إنشاء and manage reusable report templates</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Available Templates: <strong>{{ report_templates|default:0 }}</strong></span>
                        <a href="{% url 'reports:report_template_list' %}" class="btn btn-primary">
                            <i class="fas fa-eye me-1"></i>عرض Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generated Reports -->
        <div class="col-md-6">
            <div class="report-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-download me-2"></i>Generated Reports
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Access previously generated reports</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Recent Reports: <strong>{{ recent_reports|default:0 }}</strong></span>
                        <a href="{% url 'reports:generated_report_list' %}" class="btn btn-success">
                            <i class="fas fa-list me-1"></i>عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    {% if recent_generated_reports %}
    <div class="report-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-history me-2"></i>Recent Reports
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Report الاسم</th>
                            <th>Type</th>
                            <th>Generated</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for report in recent_generated_reports %}
                        <tr>
                            <td>
                                <div class="fw-bold">{{ report.name }}</div>
                                <small class="text-muted">{{ report.description|truncatechars:50 }}</small>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ report.report_type }}</span>
                            </td>
                            <td>
                                <div>{{ report.created_at|date:"M d, Y" }}</div>
                                <small class="text-muted">{{ report.created_at|time:"H:i" }}</small>
                            </td>
                            <td>
                                <span class="badge bg-success">Ready</span>
                            </td>
                            <td>
                                <a href="{% url 'reports:generated_report_detail' report.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="{% url 'reports:download_report' report.id %}" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-download me-1"></i>تحميل
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="text-center mt-3">
                <a href="{% url 'reports:generated_report_list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-1"></i>عرض All Reports
                </a>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="report-card">
        <div class="card-body text-center py-5">
            <i class="fas fa-chart-bar fa-5x text-muted mb-4"></i>
            <h3 class="text-muted mb-3">No Reports Generated Yet</h3>
            <p class="text-muted mb-4">Start generating reports to track your business performance and analytics.</p>
            <a href="{% url 'reports:generate_report' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Generate First Report
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any custom JavaScript for the reports page
console.log('Reports page loaded');
</script>
{% endblock %}