# Generated by Django 4.2.7 on 2025-09-14 12:21

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='brands/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Brand',
                'verbose_name_plural': 'Brands',
                'db_table': 'brands',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('vehicle_type', models.CharField(choices=[('motorcycle', 'Motorcycle'), ('car', 'Car'), ('tuktuk', 'Tuk-Tuk'), ('general', 'General')], default='general', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='inventory.category')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'db_table': 'categories',
                'ordering': ['vehicle_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('customer_type', models.CharField(choices=[('individual', 'Individual'), ('business', 'Business'), ('dealer', 'Dealer')], default='individual', max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('tax_number', models.CharField(blank=True, max_length=50)),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('current_balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'db_table': 'customers',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('sku', models.CharField(max_length=100, unique=True)),
                ('barcode', models.CharField(blank=True, max_length=100, unique=True)),
                ('unit', models.CharField(choices=[('piece', 'Piece'), ('set', 'Set'), ('pair', 'Pair'), ('meter', 'Meter'), ('liter', 'Liter'), ('kg', 'Kilogram'), ('box', 'Box')], default='piece', max_length=20)),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('wholesale_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('current_stock', models.IntegerField(default=0)),
                ('minimum_stock', models.IntegerField(default=0)),
                ('maximum_stock', models.IntegerField(default=1000)),
                ('reorder_level', models.IntegerField(default=10)),
                ('weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('Dimensions', models.CharField(blank=True, max_length=100)),
                ('color', models.CharField(blank=True, max_length=50)),
                ('materials', models.CharField(blank=True, max_length=100)),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('datasheet', models.FileField(blank=True, null=True, upload_to='datasheets/')),
                ('compatible_vehicles', models.TextField(blank=True, help_text='List of compatible vehicle models')),
                ('part_number', models.CharField(blank=True, max_length=100)),
                ('oem_number', models.CharField(blank=True, max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brand', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='products', to='inventory.brand')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='inventory.category')),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'db_table': 'products',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('contact_person', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('tax_number', models.CharField(blank=True, max_length=50)),
                ('payment_terms', models.CharField(blank=True, max_length=100)),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('current_balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Supplier',
                'verbose_name_plural': 'Suppliers',
                'db_table': 'suppliers',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('purchase', 'Purchase'), ('sale', 'Sale'), ('return_in', 'Return In'), ('return_out', 'Return Out'), ('adjustment', 'Stock Adjustment'), ('transfer', 'Transfer'), ('damaged', 'Damaged'), ('lost', 'Lost')], max_length=20)),
                ('quantity', models.IntegerField()),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('reference_model', models.CharField(blank=True, max_length=50)),
                ('reference_id', models.PositiveIntegerField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='inventory.product')),
            ],
            options={
                'verbose_name': 'Stock Movement',
                'verbose_name_plural': 'Stock Movements',
                'db_table': 'stock_movements',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'Low Stock'), ('out_of_stock', 'Out of Stock'), ('reorder', 'Reorder Required'), ('overstock', 'Overstock'), ('expiry', 'Expiry Alert')], max_length=20)),
                ('message', models.TextField()),
                ('current_stock', models.IntegerField()),
                ('recommended_action', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('acknowledged', 'Acknowledged'), ('resolved', 'Resolved')], default='active', max_length=20)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='inventory.product')),
            ],
            options={
                'verbose_name': 'Inventory Alert',
                'verbose_name_plural': 'Inventory Alerts',
                'db_table': 'inventory_alerts',
                'ordering': ['-created_at'],
            },
        ),
    ]
