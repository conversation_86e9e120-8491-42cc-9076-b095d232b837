{% extends 'base.html' %}


{% block title %}الإعدادات - SpareSmart{% endblock %}
{% block page_title %}الإعدادات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-cog me-3"></i>System Settings
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Language Preferences -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Language Preferences</h5>
                            <form method="post" action="{% url 'set_language' %}">
                                {% csrf_token %}
                                <input name="next" type="hidden" value="{{ request.get_full_path }}" />
                                <div class="mb-3">
                                    <label class="form-label">Select Language</label>
                                    <select name="language" class="form-select" onchange="this.form.submit()">
                                        <option value="ar" {% if request.LANGUAGE_CODE == 'ar' %}selected{% endif %}>العربية</option>
                                        <option value="en" {% if request.LANGUAGE_CODE == 'en' %}selected{% endif %}>English</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>User Preferences</h5>
                            <div class="mb-3">
                                <label class="form-label">Theme</label>
                                <select class="form-select">
                                    <option>Light</option>
                                    <option>Dark</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">التاريخ Format</label>
                                <select class="form-select">
                                    <option>DD/MM/YYYY</option>
                                    <option>MM/DD/YYYY</option>
                                    <option>YYYY-MM-DD</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- System Information -->
                    <div class="row">
                        <div class="col-12">
                            <h5>System معلومات</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Current Language:</strong></td>
                                    <td>{% if request.LANGUAGE_CODE == 'ar' %}العربية{% else %}English{% endif %}</td>
                                </tr>
                                <tr>
                                    <td><strong>Text Direction:</strong></td>
                                    <td>{% if request.LANGUAGE_BIDI %}Right to Left (RTL){% else %}Left to Right (LTR){% endif %}</td>
                                </tr>
                                <tr>
                                    <td><strong>User Role:</strong></td>
                                    <td>{{ user.get_role_display }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الأخير Login:</strong></td>
                                    <td>{{ user.last_login|date:"Y-m-d H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}