{% extends 'base.html' %}

{% load widget_tweaks %}

{% block title %}Report Templates - SpareSmart{% endblock %}
{% block page_title %}Report Templates{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .filter-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .templates-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    .templates-table th {
        background: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem;
    }
    .templates-table td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid #f1f3f4;
    }
    .templates-table tbody tr:hover {
        background: #f8f9fa;
    }
    .template-type-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .empty-state i {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    .btn-action {
        padding: 0.375rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 6px;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="fas fa-file-alt me-3"></i>قوالب التقارير
                </h1>
                <p class="mb-0 opacity-75">إدارة وإنشاء قوالب التقارير القابلة لإعادة الاستخدام</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'reports:report_template_create' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>قالب جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="البحث في القوالب..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع التقرير</label>
                <select name="report_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="sales" {% if request.GET.report_type == 'sales' %}selected{% endif %}>تقرير المبيعات</option>
                    <option value="purchases" {% if request.GET.report_type == 'purchases' %}selected{% endif %}>تقرير المشتريات</option>
                    <option value="inventory" {% if request.GET.report_type == 'inventory' %}selected{% endif %}>تقرير المخزون</option>
                    <option value="expenses" {% if request.GET.report_type == 'expenses' %}selected{% endif %}>تقرير المصروفات</option>
                    <option value="custom" {% if request.GET.report_type == 'custom' %}selected{% endif %}>تقرير مخصص</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{% url 'reports:report_template_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>

    <!-- Templates Table -->
    {% if templates %}
    <div class="templates-table">
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>
                        <input type="checkbox" id="selectAll" class="form-check-input">
                    </th>
                    <th>اسم القالب</th>
                    <th>نوع التقرير</th>
                    <th>الوصف</th>
                    <th>تكرار الجدولة</th>
                    <th>آخر تشغيل</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for template in templates %}
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input template-checkbox" value="{{ template.id }}">
                    </td>
                    <td>
                        <div>
                            <div class="fw-bold">{{ template.name }}</div>
                            <small class="text-muted">أنشئ بواسطة {{ template.created_by.get_full_name|default:template.created_by.username }}</small>
                        </div>
                    </td>
                    <td>
                        <span class="template-type-badge bg-primary text-white">
                            {{ template.get_report_type_display }}
                        </span>
                    </td>
                    <td>
                        <div class="text-muted">
                            {{ template.description|truncatechars:60|default:"لا يوجد وصف" }}
                        </div>
                    </td>
                    <td>
                        {% if template.is_scheduled %}
                            <span class="badge bg-info">{{ template.get_frequency_display }}</span>
                        {% else %}
                            <span class="text-muted">غير مجدول</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if template.last_run_date %}
                            <div>{{ template.last_run_date|date:"M d, Y" }}</div>
                            <small class="text-muted">{{ template.last_run_date|time:"H:i" }}</small>
                        {% else %}
                            <span class="text-muted">لم يتم التشغيل بعد</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if template.is_active %}
                            <span class="status-badge bg-success text-white">نشط</span>
                        {% else %}
                            <span class="status-badge bg-secondary text-white">غير نشط</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'reports:report_template_detail' template.id %}" class="btn btn-sm btn-outline-primary btn-action" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-outline-success btn-action" title="تشغيل التقرير">
                                <i class="fas fa-play"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-outline-warning btn-action" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-outline-danger btn-action" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا القالب؟')">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.report_type %}&report_type={{ request.GET.report_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأولى</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.report_type %}&report_type={{ request.GET.report_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">السابقة</a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.report_type %}&report_type={{ request.GET.report_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">التالية</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.report_type %}&report_type={{ request.GET.report_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأخيرة</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-file-alt"></i>
        <h3 class="text-muted mb-3">لا توجد قوالب تقارير</h3>
        <p class="text-muted mb-4">ابدأ بإنشاء قالب تقرير جديد لتنظيم وأتمتة تقاريرك.</p>
        <a href="{% url 'reports:report_template_create' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>إنشاء قالب جديد
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Select All functionality
document.getElementById('selectAll')?.addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.template-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Individual checkbox handling
document.querySelectorAll('.template-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const selectAll = document.getElementById('selectAll');
        const allCheckboxes = document.querySelectorAll('.template-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.template-checkbox:checked');
        
        if (checkedCheckboxes.length === allCheckboxes.length) {
            selectAll.checked = true;
            selectAll.indeterminate = false;
        } else if (checkedCheckboxes.length > 0) {
            selectAll.checked = false;
            selectAll.indeterminate = true;
        } else {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        }
    });
});

console.log('Report templates page loaded');
</script>
{% endblock %}
