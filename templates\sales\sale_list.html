{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Sales | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .filter-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .sales-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    .sales-table th {
        background: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem;
    }
    .sales-table td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid #f1f3f4;
    }
    .sales-table tbody tr:hover {
        background: #f8f9fa;
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
    }
    .stats-row {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
    .sale-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
    }
    .quick-actions {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="fas fa-shopping-cart me-3"></i>إدارة المبيعات
                </h1>
                <p class="mb-0 opacity-75">Manage sales transactions, payments, and invoices</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'sales:sale_create' %}" class="btn btn-light btn-lg me-2">
                    <i class="fas fa-plus me-2"></i>بيع جديد
                </a>
                <a href="{% url 'sales:quick_sale' %}" class="btn btn-success btn-lg">
                    <i class="fas fa-bolt me-2"></i>بيع سريع
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ total_sales }}</div>
                    <div class="stat-label">الإجمالي المبيعات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">{{ today_sales }}</div>
                    <div class="stat-label">اليوم's المبيعات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value text-success">${{ today_revenue|floatformat:2 }}</div>
                    <div class="stat-label">اليوم's Revenue</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value text-warning">${{ pending_payments|floatformat:2 }}</div>
                    <div class="stat-label">معلق Payments</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <div class="row g-2">
            <div class="col-md-2">
                <a href="{% url 'sales:sale_create' %}" class="btn btn-primary w-100">
                    <i class="fas fa-plus me-1"></i>بيع جديد
                </a>
            </div>
            <div class="col-md-2">
                <a href="{% url 'sales:quick_sale' %}" class="btn btn-success w-100">
                    <i class="fas fa-bolt me-1"></i>بيع سريع
                </a>
            </div>
            <div class="col-md-2">
                <a href="{% url 'sales:installment_list' %}" class="btn btn-info w-100">
                    <i class="fas fa-calendar-alt me-1"></i>الأقساط
                </a>
            </div>
            <div class="col-md-2">
                <a href="#" class="btn btn-warning w-100">
                    <i class="fas fa-file-invoice me-1"></i>الفاتورةs
                </a>
            </div>
            <div class="col-md-2">
                <a href="#" class="btn btn-secondary w-100">
                    <i class="fas fa-chart-bar me-1"></i>التقارير
                </a>
            </div>
            <div class="col-md-2">
                <a href="#" class="btn btn-outline-primary w-100">
                    <i class="fas fa-download me-1"></i>تصدير
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">بحث المبيعات</label>
                {{ filter_form.search }}
            </div>
            <div class="col-md-2">
                <label class="form-label">العميل</label>
                {{ filter_form.customer }}
            </div>
            <div class="col-md-2">
                <label class="form-label">Sale Type</label>
                {{ filter_form.sale_type }}
            </div>
            <div class="col-md-1">
                <label class="form-label">الحالة</label>
                {{ filter_form.status }}
            </div>
            <div class="col-md-2">
                <label class="form-label">الدفع الحالة</label>
                {{ filter_form.payment_status }}
            </div>
            <div class="col-md-2">
                <label class="form-label">نطاق التاريخ</label>
                {{ filter_form.date_range }}
            </div>
        </form>
        
        <div class="row g-3 mt-2">
            <div class="col-md-3">
                <label class="form-label"> التاريخ من</label>
                {{ filter_form.date_from }}
            </div>
            <div class="col-md-3">
                <label class="form-label"> التاريخ إلي</label>
                {{ filter_form.date_to }}
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>تصفية
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{% url 'sales:sale_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>مسح
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Table -->
    {% if sales %}
    <div class="sales-table">
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>رقم البيع</th>
                    <th>العميل</th>
                    <th>التاريخ</th>
                    <th>Type</th>
                    <th>العناصر</th>
                    <th>المبلغ</th>
                    <th>مدفوع</th>
                    <th>الرصيد</th>
                    <th>الحالة</th>
                    <th>الدفع</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for sale in sales %}
                <tr>
                    <td>
                        <a href="{% url 'sales:sale_detail' sale.id %}" class="fw-bold text-decoration-none">
                            {{ sale.sale_number }}
                        </a>
                    </td>
                    <td>
                        <div>
                            <div class="fw-bold">{{ sale.customer.name }}</div>
                            <small class="text-muted">{{ sale.customer.phone }}</small>
                        </div>
                    </td>
                    <td>
                        <div>{{ sale.sale_date|date:"M d, Y" }}</div>
                        <small class="text-muted">{{ sale.sale_date|time:"H:i" }}</small>
                    </td>
                    <td>
                        <span class="badge sale-type-badge 
                            {% if sale.sale_type == 'cash' %}bg-success
                            {% elif sale.sale_type == 'credit' %}bg-warning
                            {% elif sale.sale_type == 'installment' %}bg-info
                            {% else %}bg-primary{% endif %}">
                            {{ sale.get_sale_type_display }}
                        </span>
                    </td>
                    <td>
                        <span class="badge bg-light text-dark">{{ sale.items.count }} عنصر</span>
                    </td>
                    <td>
                        <div class="fw-bold">${{ sale.total_amount|floatformat:2 }}</div>
                        {% if sale.discount_amount > 0 %}
                        <small class="text-success">-${{ sale.discount_amount|floatformat:2 }} خصم</small>
                        {% endif %}
                    </td>
                    <td>
                        <div class="text-success fw-bold">${{ sale.paid_amount|floatformat:2 }}</div>
                    </td>
                    <td>
                        {% if sale.balance_amount > 0 %}
                            <div class="text-danger fw-bold">${{ sale.balance_amount|floatformat:2 }}</div>
                        {% else %}
                            <div class="text-muted">$0.00</div>
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge status-badge 
                            {% if sale.status == 'completed' %}bg-success
                            {% elif sale.status == 'pending' %}bg-warning
                            {% elif sale.status == 'cancelled' %}bg-danger
                            {% else %}bg-secondary{% endif %}">
                            {{ sale.get_status_display }}
                        </span>
                    </td>
                    <td>
                        <span class="badge status-badge 
                            {% if sale.payment_status == 'paid' %}bg-success
                            {% elif sale.payment_status == 'partial' %}bg-warning
                            {% elif sale.payment_status == 'overdue' %}bg-danger
                            {% else %}bg-secondary{% endif %}">
                            {{ sale.get_payment_status_display }}
                        </span>
                    </td>
                    <td>
                        <div class="dropdown">
                            <button class="btn btn-link btn-sm" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'sales:sale_detail' sale.id %}">
                                    <i class="fas fa-eye me-2"></i>عرض Details</a></li>
                                {% if sale.status != 'completed' %}
                                <li><a class="dropdown-item" href="{% url 'sales:sale_update' sale.id %}">
                                    <i class="fas fa-edit me-2"></i>تعديل</a></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{% url 'sales:sale_invoice' sale.id %}">
                                    <i class="fas fa-file-invoice me-2"></i>عرض الفاتورة</a></li>
                                <li><a class="dropdown-item" href="{% url 'sales:sale_print' sale.id %}" target="_blank">
                                    <i class="fas fa-print me-2"></i>طباعة الفاتورة</a></li>
                                {% if sale.balance_amount > 0 %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'sales:payment_create' sale.id %}">
                                    <i class="fas fa-credit-card me-2"></i>Record الدفع</a></li>
                                {% endif %}
                                {% if sale.sale_type == 'installment' %}
                                <li><a class="dropdown-item" href="{% url 'sales:installment_detail' sale.installment_plan.id %}">
                                    <i class="fas fa-calendar-alt me-2"></i>Installment Plan</a></li>
                                {% endif %}
                            </ul>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Sales pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
        <h3 class="text-muted mb-3">لا توجد مبيعات</h3>
        <p class="text-muted mb-4">Start making sales by creating your first transaction.</p>
        <a href="{% url 'sales:sale_create' %}" class="btn btn-primary btn-lg me-3">
            <i class="fas fa-plus me-2"></i>إنشاء First Sale
        </a>
        <a href="{% url 'sales:quick_sale' %}" class="btn btn-success btn-lg">
            <i class="fas fa-bolt me-2"></i>بيع سريع
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name="customer"], select[name="sale_type"], select[name="status"], select[name="payment_status"], select[name="date_range"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Date range change handler
    const dateRangeSelect = document.querySelector('select[name="date_range"]');
    const dateFromInput = document.querySelector('input[name="date_from"]');
    const dateToInput = document.querySelector('input[name="date_to"]');
    
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            if (this.value) {
                dateFromInput.value = '';
                dateToInput.value = '';
            }
        });
    }
});
</script>
{% endblock %}