# Generated by Django 4.2.7 on 2025-09-14 12:21

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_number', models.CharField(max_length=50, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('ordered', 'Ordered'), ('partial_received', 'Partially Received'), ('received', 'Received'), ('cancelled', 'Cancelled'), ('returned', 'Returned')], default='pending', max_length=20)),
                ('payment_status', models.CharField(choices=[('paid', 'Paid'), ('partial', 'Partially Paid'), ('unpaid', 'Unpaid'), ('overdue', 'Overdue')], default='unpaid', max_length=20)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('balance_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('order_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('actual_delivery_date', models.DateField(blank=True, null=True)),
                ('payment_due_date', models.DateField(blank=True, null=True)),
                ('supplier_invoice_number', models.CharField(blank=True, max_length=100)),
                ('supplier_reference', models.CharField(blank=True, max_length=100)),
                ('delivery_address', models.TextField(blank=True)),
                ('notes', models.TextField(blank=True)),
                ('internal_notes', models.TextField(blank=True)),
                ('terms_and_conditions', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_purchases', to=settings.AUTH_USER_MODEL)),
                ('received_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='received_purchases', to=settings.AUTH_USER_MODEL)),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchases', to='inventory.supplier')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='updated_purchases', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Purchase',
                'verbose_name_plural': 'Purchases',
                'db_table': 'purchases',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_ordered', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('quantity_received', models.IntegerField(default=0)),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quality_check_passed', models.BooleanField(default=True)),
                ('quality_notes', models.TextField(blank=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchase')),
            ],
            options={
                'verbose_name': 'Purchase Item',
                'verbose_name_plural': 'Purchase Items',
                'db_table': 'purchase_items',
            },
        ),
        migrations.CreateModel(
            name='PurchaseReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_number', models.CharField(max_length=50, unique=True)),
                ('return_type', models.CharField(choices=[('defective', 'Defective Product'), ('wrong_item', 'Wrong Item'), ('excess', 'Excess Quantity'), ('damaged', 'Damaged in Transit'), ('quality', 'Quality Issues'), ('other', 'Other')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('shipped', 'Shipped'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('refund_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('reason', models.TextField()),
                ('supplier_approval', models.TextField(blank=True)),
                ('return_date', models.DateField(default=django.utils.timezone.now)),
                ('refund_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='returns', to='purchases.purchase')),
            ],
            options={
                'verbose_name': 'Purchase Return',
                'verbose_name_plural': 'Purchase Returns',
                'db_table': 'purchase_returns',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('purchase_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.purchaseitem')),
                ('return_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchasereturn')),
            ],
            options={
                'verbose_name': 'Purchase Return Item',
                'verbose_name_plural': 'Purchase Return Items',
                'db_table': 'purchase_return_items',
            },
        ),
        migrations.CreateModel(
            name='PurchasePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_number', models.CharField(max_length=50, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank_transfer', 'Bank Transfer'), ('check', 'Check'), ('credit_card', 'Credit Card'), ('trade_credit', 'Trade Credit')], max_length=20)),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='completed', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('bank_name', models.CharField(blank=True, max_length=100)),
                ('check_number', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('paid_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_payments', to='purchases.purchase')),
            ],
            options={
                'verbose_name': 'Purchase Payment',
                'verbose_name_plural': 'Purchase Payments',
                'db_table': 'purchase_payments',
                'ordering': ['-created_at'],
            },
        ),
    ]
