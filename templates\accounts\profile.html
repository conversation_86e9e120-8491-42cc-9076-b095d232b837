{% extends 'base.html' %}

{% load widget_tweaks %}

{% block title %}الملف الشخصي - SpareSmart{% endblock %}
{% block page_title %}الملف الشخصي{% endblock %}

{% block extra_css %}
<style>
    .profile-header {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .profile-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .profile-info {
        padding: 2rem;
    }
    .info-item {
        padding: 1rem 0;
        border-bottom: 1px solid #f1f3f4;
    }
    .info-item:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    .info-value {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <img src="{% if user.profile_image %}{{ user.profile_image.url }}{% else %}https://via.placeholder.com/120{% endif %}" 
                         alt="الملف الشخصي" class="profile-avatar me-4">
                    <div>
                        <h1 class="mb-1">{{ user.first_name }} {{ user.last_name }}</h1>
                        <p class="mb-2 opacity-75">{{ user.get_role_display }}</p>
                        <span class="badge bg-light text-dark">{{ user.username }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'accounts:change_password' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-key me-2"></i>Change Password
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Personal Information -->
        <div class="col-md-6">
            <div class="profile-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Personal Information
                    </h5>
                </div>
                <div class="profile-info">
                    <div class="info-item">
                        <div class="info-label">الأول الاسم</div>
                        <div class="info-value">{{ user.first_name|default:"-" }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الأخير الاسم</div>
                        <div class="info-value">{{ user.last_name|default:"-" }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">البريد الإلكتروني</div>
                        <div class="info-value">{{ user.email|default:"-" }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الهاتف</div>
                        <div class="info-value">{{ user.phone|default:"-" }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">إضافةress</div>
                        <div class="info-value">{{ user.address|default:"-" }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="col-md-6">
            <div class="profile-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Account Information
                    </h5>
                </div>
                <div class="profile-info">
                    <div class="info-item">
                        <div class="info-label">اسم المستخدم</div>
                        <div class="info-value">{{ user.username }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Role</div>
                        <div class="info-value">
                            <span class="badge bg-primary">{{ user.get_role_display }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الحالة</div>
                        <div class="info-value">
                            <span class="badge {% if user.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                {% if user.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">التاريخ Joined</div>
                        <div class="info-value">{{ user.date_joined|date:"Y-m-d H:i" }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الأخير Login</div>
                        <div class="info-value">
                            {% if user.last_login %}
                                {{ user.last_login|date:"Y-m-d H:i" }}
                            {% else %}
                                Never
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="profile-card">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-history me-2"></i>Recent Activity
            </h5>
        </div>
        <div class="profile-info">
            <div class="text-center py-4">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">No recent activity</h6>
                <p class="text-muted">Your activity will appear here when you use the system.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}