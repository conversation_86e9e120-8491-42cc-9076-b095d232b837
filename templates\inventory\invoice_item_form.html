{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}{{ title }} - SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.2s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    .btn-save:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        color: white;
    }
    .btn-cancel {
        background: #6c757d;
        border: none;
        color: white;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
    }
    .invoice-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 2rem;
        border-right: 4px solid #667eea;
    }
    .price-preview {
        background: #e3f2fd;
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
        border: 1px solid #bbdefb;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Form Header -->
    <div class="form-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">{{ title }}</h2>
                <p class="mb-0 opacity-75">إدارة عناصر الفاتورة</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'inventory:invoice_detail' invoice.id %}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للفاتورة
                </a>
            </div>
        </div>
    </div>

    <!-- Invoice Info -->
    <div class="invoice-info">
        <div class="row">
            <div class="col-md-3">
                <strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}
            </div>
            <div class="col-md-3">
                <strong>النوع:</strong> {{ invoice.get_invoice_type_display }}
            </div>
            <div class="col-md-3">
                <strong>{{ invoice.is_sale|yesno:"العميل,المورد" }}:</strong> {{ invoice.client_name }}
            </div>
            <div class="col-md-3">
                <strong>الحالة:</strong> 
                <span class="badge bg-secondary">{{ invoice.get_status_display }}</span>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="form-card">
        <form method="post" id="invoice-item-form">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="{{ form.product.id_for_label }}">{{ form.product.label }}</label>
                        {{ form.product|add_class:"form-select" }}
                        {% if form.product.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.product.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="{{ form.quantity.id_for_label }}">{{ form.quantity.label }}</label>
                        {{ form.quantity|add_class:"form-control" }}
                        {% if form.quantity.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.quantity.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="{{ form.unit_price.id_for_label }}">{{ form.unit_price.label }}</label>
                        <div class="input-group">
                            {{ form.unit_price|add_class:"form-control" }}
                            <span class="input-group-text">ج.م</span>
                        </div>
                        {% if form.unit_price.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.unit_price.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="{{ form.discount_percentage.id_for_label }}">{{ form.discount_percentage.label }}</label>
                        <div class="input-group">
                            {{ form.discount_percentage|add_class:"form-control" }}
                            <span class="input-group-text">%</span>
                        </div>
                        {% if form.discount_percentage.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.discount_percentage.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <label class="form-label" for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                        {{ form.notes|add_class:"form-control" }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Price Preview -->
            <div class="price-preview" id="price-preview" style="display: none;">
                <div class="row">
                    <div class="col-md-3">
                        <strong>المجموع الفرعي:</strong>
                        <span id="subtotal">0.00 ج.م</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الخصم:</strong>
                        <span id="discount">0.00 ج.م</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الإجمالي:</strong>
                        <span id="total" class="fw-bold">0.00 ج.م</span>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <a href="{% url 'inventory:invoice_detail' invoice.id %}" class="btn btn-cancel">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-save">
                    <i class="fas fa-save me-2"></i>حفظ العنصر
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculate totals when inputs change
function calculateTotals() {
    const quantity = parseFloat(document.getElementById('{{ form.quantity.id_for_label }}').value) || 0;
    const unitPrice = parseFloat(document.getElementById('{{ form.unit_price.id_for_label }}').value) || 0;
    const discountPercentage = parseFloat(document.getElementById('{{ form.discount_percentage.id_for_label }}').value) || 0;
    
    const subtotal = quantity * unitPrice;
    const discountAmount = (subtotal * discountPercentage) / 100;
    const total = subtotal - discountAmount;
    
    if (quantity > 0 && unitPrice > 0) {
        document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ج.م';
        document.getElementById('discount').textContent = discountAmount.toFixed(2) + ' ج.م';
        document.getElementById('total').textContent = total.toFixed(2) + ' ج.م';
        document.getElementById('price-preview').style.display = 'block';
    } else {
        document.getElementById('price-preview').style.display = 'none';
    }
}

// Add event listeners
document.getElementById('{{ form.quantity.id_for_label }}').addEventListener('input', calculateTotals);
document.getElementById('{{ form.unit_price.id_for_label }}').addEventListener('input', calculateTotals);
document.getElementById('{{ form.discount_percentage.id_for_label }}').addEventListener('input', calculateTotals);

// Calculate on page load if editing
document.addEventListener('DOMContentLoaded', function() {
    calculateTotals();
});
</script>
{% endblock %}
