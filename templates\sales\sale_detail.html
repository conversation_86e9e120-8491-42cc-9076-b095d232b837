{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Sale Details - {{ sale.sale_number }} | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .sale-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .info-card {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        border: none;
        margin-bottom: 1.5rem;
    }
    .info-card .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
    }
    .status-badge {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        text-align: center;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .action-buttons .btn {
        margin: 0.25rem;
        border-radius: 8px;
        padding: 0.5rem 1rem;
    }
    .items-table {
        font-size: 0.9rem;
    }
    .payment-table {
        font-size: 0.9rem;
    }
    .installment-table {
        font-size: 0.9rem;
    }
    .amount-positive { color: #28a745; }
    .amount-negative { color: #dc3545; }
    .amount-zero { color: #6c757d; }
    .profit-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Sale Header -->
    <div class="sale-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">رقم البيع{{ sale.sale_number }}</h1>
                <p class="mb-0 opacity-75">
                    Customer: {{ sale.customer.name }} | 
                    Date: {{ sale.sale_date|date:"M d, Y H:i" }}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <span class="badge status-badge 
                    {% if sale.status == 'completed' %}bg-success
                    {% elif sale.status == 'pending' %}bg-warning
                    {% elif sale.status == 'cancelled' %}bg-danger
                    {% else %}bg-secondary{% endif %}">
                    {{ sale.get_status_display }}
                </span>
                <span class="badge status-badge 
                    {% if sale.payment_status == 'paid' %}bg-success
                    {% elif sale.payment_status == 'partial' %}bg-warning
                    {% elif sale.payment_status == 'overdue' %}bg-danger
                    {% else %}bg-secondary{% endif %} ms-2">
                    {{ sale.get_payment_status_display }}
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Sale Information -->
        <div class="col-lg-8">
            <!-- Sale Information -->
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Sale معلومات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Sale Number</label>
                                <p class="fw-bold">{{ sale.sale_number }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">العميل</label>
                                <p>
                                    <a href="{% url 'inventory:customer_detail' sale.customer.id %}" class="text-decoration-none">
                                        {{ sale.customer.name }}
                                    </a>
                                    <br><small class="text-muted">{{ sale.customer.phone }}</small>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Sale Type</label>
                                <p>{{ sale.get_sale_type_display }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Sale التاريخ</label>
                                <p>{{ sale.sale_date|date:"M d, Y H:i" }}</p>
                            </div>
                            {% if sale.due_date %}
                            <div class="mb-3">
                                <label class="form-label text-muted">مستحق التاريخ</label>
                                <p>{{ sale.due_date|date:"M d, Y" }}</p>
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <label class="form-label text-muted">إنشاءd By</label>
                                <p>{{ sale.created_by.get_full_name }}</p>
                            </div>
                        </div>
                    </div>
                    
                    {% if sale.notes %}
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">ملاحظات العميل</label>
                                <p>{{ sale.notes }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Sale Items -->
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>بيع العناصر</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover items-table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    <th>الخصم</th>
                                    <th>الإجمالي</th>
                                    <th>ربح</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sale.items.all %}
                                <tr>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ item.product.name }}</div>
                                            <small class="text-muted">{{ item.product.sku }}</small>
                                        </div>
                                    </td>
                                    <td>{{ item.quantity }}</td>
                                    <td>${{ item.unit_price|floatformat:2 }}</td>
                                    <td>
                                        {% if item.discount_percentage > 0 %}
                                            {{ item.discount_percentage }}%
                                            <br><small class="text-success">-${{ item.discount_amount|floatformat:2 }}</small>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="fw-bold">${{ item.total_price|floatformat:2 }}</td>
                                    <td>
                                        {% with profit=item.profit %}
                                        {% if profit > 0 %}
                                            <span class="profit-badge bg-success text-white">
                                                +${{ profit|floatformat:2 }}
                                            </span>
                                        {% elif profit < 0 %}
                                            <span class="profit-badge bg-danger text-white">
                                                ${{ profit|floatformat:2 }}
                                            </span>
                                        {% else %}
                                            <span class="profit-badge bg-secondary text-white">
                                                $0.00
                                            </span>
                                        {% endif %}
                                        {% endwith %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payments -->
            <div class="card info-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>الدفع</h5>
                    {% if sale.balance_amount > 0 %}
                    <a href="{% url 'sales:payment_create' sale.id %}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-1"></i>تسجيل دفعة
                    </a>
                    {% endif %}
                </div>
                <div class="card-body p-0">
                    {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-hover payment-table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الدفع #</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>Method</th>
                                    <th>Reference</th>
                                    <th>مستلم By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td class="fw-bold">{{ payment.payment_number }}</td>
                                    <td>{{ payment.payment_date|date:"M d, Y H:i" }}</td>
                                    <td class="fw-bold text-success">${{ payment.amount|floatformat:2 }}</td>
                                    <td>{{ payment.get_payment_method_display }}</td>
                                    <td>{{ payment.reference_number|default:"-" }}</td>
                                    <td>{{ payment.received_by.get_full_name }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No payments recorded yet.</p>
                        {% if sale.balance_amount > 0 %}
                        <a href="{% url 'sales:payment_create' sale.id %}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Record First Payment
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Installment Plan (if applicable) -->
            {% if installment_plan %}
            <div class="card info-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Installment Plan</h5>
                    <a href="{% url 'sales:installment_detail' installment_plan.id %}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye me-1"></i>عرض Details
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">الإجمالي المبلغ</label>
                                <p class="fw-bold">${{ installment_plan.total_amount|floatformat:2 }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">Down الدفع</label>
                                <p class="fw-bold">${{ installment_plan.down_payment|floatformat:2 }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">Installment المبلغ</label>
                                <p class="fw-bold">${{ installment_plan.installment_amount|floatformat:2 }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">الأقساط</label>
                                <p class="fw-bold">{{ installment_plan.paid_installments }}/{{ installment_plan.number_of_installments }}</p>
                            </div>
                        </div>
                    </div>

                    {% if installment_payments %}
                    <div class="table-responsive">
                        <table class="table table-sm installment-table">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>مستحق التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>مدفوع</th>
                                    <th>الحالة</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in installment_payments %}
                                <tr>
                                    <td>{{ payment.installment_number }}</td>
                                    <td>{{ payment.due_date|date:"M d, Y" }}</td>
                                    <td>${{ payment.amount|floatformat:2 }}</td>
                                    <td>${{ payment.paid_amount|floatformat:2 }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if payment.status == 'paid' %}bg-success
                                            {% elif payment.status == 'overdue' %}bg-danger
                                            {% elif payment.status == 'partial' %}bg-warning
                                            {% else %}bg-secondary{% endif %}">
                                            {{ payment.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if payment.status != 'paid' %}
                                        <a href="{% url 'sales:installment_payment' payment.id %}" class="btn btn-success btn-xs">
                                            Pay
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar Stats -->
        <div class="col-lg-4">
            <!-- Financial Summary -->
            <div class="stat-card">
                <div class="stat-value">${{ sale.total_amount|floatformat:2 }}</div>
                <div class="stat-label">الإجمالي المبلغ</div>
            </div>

            <div class="row">
                <div class="col-6">
                    <div class="stat-card bg-success">
                        <div class="stat-value">${{ sale.paid_amount|floatformat:2 }}</div>
                        <div class="stat-label">مدفوع</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-card {% if sale.balance_amount > 0 %}bg-danger{% else %}bg-secondary{% endif %}">
                        <div class="stat-value">${{ sale.balance_amount|floatformat:2 }}</div>
                        <div class="stat-label">الرصيد</div>
                    </div>
                </div>
            </div>

            <!-- Sale Summary -->
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>ملخص البيع</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">المجموع الفرعي</label>
                        <p class="fw-bold">${{ sale.subtotal|floatformat:2 }}</p>
                    </div>
                    {% if sale.discount_amount > 0 %}
                    <div class="mb-3">
                        <label class="form-label text-muted">الخصم</label>
                        <p class="fw-bold text-success">-${{ sale.discount_amount|floatformat:2 }}</p>
                    </div>
                    {% endif %}
                    <div class="mb-3">
                        <label class="form-label text-muted">الإجمالي العناصر</label>
                        <p>{{ sale.items.count }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">الإجمالي Profit</label>
                        <p class="fw-bold text-success">${{ sale.profit|floatformat:2 }}</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tools me-2"></i>الإجراءات</h6>
                </div>
                <div class="card-body action-buttons">
                    {% if sale.status != 'completed' %}
                    <a href="{% url 'sales:sale_update' sale.id %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>تعديل Sale
                    </a>
                    {% endif %}
                    <a href="{% url 'sales:sale_invoice' sale.id %}" class="btn btn-info btn-sm">
                        <i class="fas fa-file-invoice me-1"></i>عرض Invoice
                    </a>
                    <a href="{% url 'sales:sale_print' sale.id %}" target="_blank" class="btn btn-success btn-sm">
                        <i class="fas fa-print me-1"></i>طباعة Invoice
                    </a>
                    {% if sale.balance_amount > 0 %}
                    <a href="{% url 'sales:payment_create' sale.id %}" class="btn btn-warning btn-sm">
                        <i class="fas fa-credit-card me-1"></i>تسجيل دفعة
                    </a>
                    {% endif %}
                    {% if sale.sale_type == 'installment' and installment_plan %}
                    <a href="{% url 'sales:installment_detail' installment_plan.id %}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-calendar-alt me-1"></i>الأقساط
                    </a>
                    {% endif %}
                    <a href="{% url 'sales:sale_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>رجوع إلي البيع
                    </a>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="card info-card">
                <div class="card-body">
                    <small class="text-muted">
                        <div class="mb-1">
                            <strong>إنشاءd:</strong> {{ sale.created_at|date:"M d, Y H:i" }}
                        </div>
                        <div>
                            <strong>تحديثd:</strong> {{ sale.updated_at|date:"M d, Y H:i" }}
                        </div>
                        {% if sale.updated_by %}
                        <div>
                            <strong>تحديثd by:</strong> {{ sale.updated_by.get_full_name }}
                        </div>
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive functionality here
    console.log('Sale detail page loaded');
});
</script>
{% endblock %}