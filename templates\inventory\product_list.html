{% extends 'base.html' %}


{% block title %}المنتجات - سبير سمارت{% endblock %}
{% block page_title %}Product Management{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-boxes"></i> المنتجات</h5>
                <div>
                    <a href="{% url 'inventory:product_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </a>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i> الإجراءات
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'inventory:category_list' %}"><i class="fas fa-list"></i> Manage Categories</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:low_stock_report' %}"><i class="fas fa-exclamation-triangle"></i> Low Stock Report</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:stock_movements' %}"><i class="fas fa-exchange-alt"></i> Stock Movements</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <form method="get" class="mb-4">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            {{ filter_form.search }}
                        </div>
                        <div class="col-md-2 mb-2">
                            {{ filter_form.category }}
                        </div>
                        <div class="col-md-2 mb-2">
                            {{ filter_form.brand }}
                        </div>
                        <div class="col-md-2 mb-2">
                            {{ filter_form.vehicle_type }}
                        </div>
                        <div class="col-md-2 mb-2">
                            {{ filter_form.stock_status }}
                        </div>
                        <div class="col-md-1 mb-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>إجمالي المنتجات</h6>
                                        <h4>{{ total_products }}</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-boxes fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>الإجمالي العام</h6>
                                        <h4>${{ total_value|floatformat:2 }}</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>مخزون منخفض العناصر</h6>
                                        <h4>{{ page_obj.object_list|length }}</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div id="bulk-actions" style="display: none;">
                                <form method="post" action="{% url 'inventory:bulk_action' %}" id="bulk-form">
                                    {% csrf_token %}
                                    <input type="hidden" name="selected_products" id="selected-products">
                                    <div class="input-group">
                                        <select name="action" class="form-select" required>
                                            <option value="">Select Action</option>
                                            <option value="activate">Activate</option>
                                            <option value="deactivate">Deactivate</option>
                                            <option value="delete">حذف</option>
                                        </select>
                                        <button type="submit" class="btn btn-warning">تطبيق</button>
                                    </div>
                                </form>
                            </div>
                            <div>
                                <span class="text-muted">{{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} منتج</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" class="form-check-input">
                                </th>
                                <th>الصورة</th>
                                <th>المنتج</th>
                                <th>رمز المنتج</th>
                                <th>الفئة</th>
                                <th>العلامة التجارية</th>
                                <th>المخزون</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in page_obj %}
                            <tr>
                                <td>
                                    <input type="checkbox" name="product_select" value="{{ product.id }}" class="form-check-input product-checkbox">
                                </td>
                                <td>
                                    {% if product.image %}
                                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong><a href="{% url 'inventory:product_detail' product.id %}" class="text-decoration-none">{{ product.name }}</a></strong>
                                        {% if product.description %}
                                            <br><small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td><code>{{ product.sku }}</code></td>
                                <td>
                                    <span class="badge bg-info">{{ product.category.name }}</span>
                                    <br><small class="text-muted">{{ product.category.get_vehicle_type_display }}</small>
                                </td>
                                <td>{{ product.brand|default:"-" }}</td>
                                <td>
                                    {% if product.current_stock == 0 %}
                                        <span class="badge bg-danger">Out of المخزون</span>
                                    {% elif product.current_stock <= product.reorder_level %}
                                        <span class="badge bg-warning">{{ product.current_stock }} {{ product.unit }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ product.current_stock }} {{ product.unit }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>${{ product.selling_price }}</strong>
                                    {% if product.wholesale_price %}
                                        <br><small class="text-muted">Wholesale: ${{ product.wholesale_price }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                    {% if product.is_featured %}
                                        <br><span class="badge bg-primary">Featured</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:product_update' product.id %}" class="btn btn-sm btn-outline-secondary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-box-open fa-3x mb-3"></i>
                                        <h5>No products found</h5>
                                        <p>Try adjusting your search criteria or <a href="{% url 'inventory:product_create' %}">add a new product</a>.</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Product pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">الأول</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">السابق</a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">التالي</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">الأخير</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Select All functionality
    $('#select-all').change(function() {
        $('.product-checkbox').prop('checked', this.checked);
        toggleBulkActions();
    });

    // Individual checkbox change
    $('.product-checkbox').change(function() {
        toggleBulkActions();
        
        // Update select all checkbox
        const total = $('.product-checkbox').length;
        const checked = $('.product-checkbox:checked').length;
        $('#select-all').prop('indeterminate', checked > 0 && checked < total);
        $('#select-all').prop('checked', checked === total);
    });

    // Toggle bulk actions visibility
    function toggleBulkActions() {
        const selectedCount = $('.product-checkbox:checked').length;
        if (selectedCount > 0) {
            $('#bulk-actions').show();
            updateSelectedProducts();
        } else {
            $('#bulk-actions').hide();
        }
    }

    // Update selected products input
    function updateSelectedProducts() {
        const selected = $('.product-checkbox:checked').map(function() {
            return this.value;
        }).get();
        $('#selected-products').val(selected.join(','));
    }

    // Bulk form submission
    $('#bulk-form').submit(function(e) {
        const selectedCount = $('.product-checkbox:checked').length;
        const action = $('select[name="action"]').val();
        
        if (selectedCount === 0) {
            e.preventDefault();
            alert('Please select at least one product.');
            return false;
        }
        
        if (action === 'delete') {
            if (!confirm(`Are you sure you want to delete ${selectedCount} selected product(s)? This action cannot be undone.`)) {
                e.preventDefault();
                return false;
            }
        } else if (action) {
            if (!confirm(`Are you sure you want to ${action} ${selectedCount} selected product(s)?`)) {
                e.preventDefault();
                return false;
            }
        }
        
        updateSelectedProducts();
    });

    // Clear filters
    $('.btn-clear-filters').click(function() {
        window.location.href = '{% url "inventory:product_list" %}';
    });
});
</script>
{% endblock %}