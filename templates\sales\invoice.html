{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}فاتورة رقم {{ sale.sale_number }} | SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .invoice-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        padding: 2.5rem;
        margin: 2rem 0;
        position: relative;
        overflow: hidden;
    }
    
    .invoice-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }
    
    .invoice-header {
        border-bottom: 3px solid #667eea;
        padding-bottom: 2rem;
        margin-bottom: 2.5rem;
        position: relative;
    }
    
    .company-logo {
        font-size: 2.8rem;
        color: #667eea;
        margin-bottom: 1rem;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(102, 126, 234, 0.1);
    }
    
    .company-details {
        color: #6c757d;
        font-size: 0.95rem;
        line-height: 1.6;
    }
    
    .invoice-title {
        font-size: 3.2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(102, 126, 234, 0.1);
    }
    
    .invoice-number {
        font-size: 1.3rem;
        color: #495057;
        font-weight: 600;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        display: inline-block;
        margin-bottom: 1rem;
    }
    
    .info-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        border-right: 4px solid #667eea;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .info-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .info-title {
        font-weight: bold;
        color: #667eea;
        margin-bottom: 1.5rem;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .info-title i {
        font-size: 1.1rem;
    }
    
    .info-item {
        margin-bottom: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .info-item strong {
        color: #495057;
        min-width: 100px;
        display: inline-block;
    }
    
    .items-table {
        margin: 2.5rem 0;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .items-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        padding: 1.2rem 1rem;
        border: none;
        text-align: center;
        font-size: 0.95rem;
    }
    
    .items-table td {
        padding: 1.2rem 1rem;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
        text-align: center;
        transition: background-color 0.2s ease;
    }
    
    .items-table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .items-table .text-start {
        text-align: right !important;
    }
    
    .product-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.3rem;
    }
    
    .product-sku {
        font-size: 0.85rem;
        color: #6c757d;
        font-style: italic;
    }
    
    .totals-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 12px;
        padding: 2rem;
        border-right: 4px solid #28a745;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    
    .total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.8rem 0;
        border-bottom: 1px solid #dee2e6;
        font-size: 1.05rem;
    }
    
    .total-row:last-child {
        border-bottom: 3px solid #28a745;
        font-weight: bold;
        font-size: 1.4rem;
        color: #28a745;
        margin-top: 1rem;
        padding-top: 1.5rem;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
        border-radius: 8px;
        padding: 1rem;
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.6rem 1.2rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .status-completed { 
        background: linear-gradient(135deg, #d1edff 0%, #a8daff 100%); 
        color: #0c63e4; 
        border: 1px solid #0c63e4;
    }
    .status-pending { 
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); 
        color: #856404; 
        border: 1px solid #856404;
    }
    .status-cancelled { 
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); 
        color: #721c24; 
        border: 1px solid #721c24;
    }
    
    .payment-paid { 
        background: linear-gradient(135deg, #d1e7dd 0%, #badbcc 100%); 
        color: #0f5132; 
        border: 1px solid #0f5132;
    }
    .payment-partial { 
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); 
        color: #856404; 
        border: 1px solid #856404;
    }
    .payment-unpaid { 
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); 
        color: #721c24; 
        border: 1px solid #721c24;
    }
    .payment-overdue { 
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); 
        color: white; 
        border: 1px solid #dc3545;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
        100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    .action-buttons {
        margin: 2.5rem 0;
        text-align: center;
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }
    
    .action-btn {
        padding: 0.8rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: none;
        cursor: pointer;
        font-size: 0.95rem;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-primary-custom:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .btn-success-custom {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }
    
    .btn-success-custom:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .btn-info-custom {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
    }
    
    .btn-info-custom:hover {
        background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(23, 162, 184, 0.3);
        color: white;
    }
    
    .btn-secondary-custom {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        color: white;
    }
    
    .btn-secondary-custom:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
        color: white;
    }
    
    .payment-history {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 12px;
        padding: 2rem;
        margin-top: 2rem;
        border-right: 4px solid #17a2b8;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .payment-history-title {
        font-weight: bold;
        color: #17a2b8;
        margin-bottom: 1.5rem;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .payment-table th {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        font-weight: 600;
        padding: 1rem;
        border: none;
        text-align: center;
    }
    
    .payment-table td {
        padding: 1rem;
        text-align: center;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
    }
    
    .payment-table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .notes-section {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 12px;
        padding: 2rem;
        margin-top: 2rem;
        border-right: 4px solid #ffc107;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .notes-title {
        font-weight: bold;
        color: #856404;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .invoice-footer {
        border-top: 2px solid #e9ecef;
        padding-top: 2rem;
        margin-top: 3rem;
        text-align: center;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    @media (max-width: 768px) {
        .invoice-container {
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .action-btn {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
        
        .invoice-title {
            font-size: 2.5rem;
        }
        
        .company-logo {
            font-size: 2.2rem;
        }
        
        .items-table {
            font-size: 0.9rem;
        }
        
        .items-table th,
        .items-table td {
            padding: 0.8rem 0.5rem;
        }
    }
    
    .breadcrumb-custom {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }
    
    .breadcrumb-custom a:hover {
        color: #5a6fd8;
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-custom">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{% url 'dashboard:home' %}">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'sales:sale_list' %}">
                    <i class="fas fa-shopping-cart"></i> المبيعات
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'sales:sale_detail' sale.id %}">
                    <i class="fas fa-eye"></i> تفاصيل البيع
                </a>
            </li>
            <li class="breadcrumb-item active">
                <i class="fas fa-file-invoice"></i> الفاتورة
            </li>
        </ol>
    </nav>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'sales:sale_print' sale.id %}" class="action-btn btn-primary-custom" target="_blank">
            <i class="fas fa-print"></i> طباعة الفاتورة
        </a>
        <a href="{% url 'sales:sale_detail' sale.id %}" class="action-btn btn-info-custom">
            <i class="fas fa-eye"></i> تفاصيل البيع
        </a>
        {% if sale.payment_status != 'paid' %}
        <a href="{% url 'sales:payment_create' sale.id %}" class="action-btn btn-success-custom">
            <i class="fas fa-credit-card"></i> إضافة دفعة
        </a>
        {% endif %}
        <a href="{% url 'sales:sale_list' %}" class="action-btn btn-secondary-custom">
            <i class="fas fa-list"></i> قائمة المبيعات
        </a>
    </div>

    <!-- Invoice Container -->
    <div class="invoice-container">
        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="row">
                <div class="col-md-6">
                    <div class="company-logo">
                        <i class="fas fa-cogs"></i> {{ company_info.name }}
                    </div>
                    <div class="company-details">
                        <div><i class="fas fa-map-marker-alt"></i> {{ company_info.address }}</div>
                        <div><i class="fas fa-phone"></i> {{ company_info.phone }}</div>
                        <div><i class="fas fa-envelope"></i> {{ company_info.email }}</div>
                    </div>
                </div>
                <div class="col-md-6 text-start">
                    <div class="invoice-title">فاتورة</div>
                    <div class="invoice-number">
                        <i class="fas fa-hashtag"></i> {{ sale.sale_number }}
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-calendar-alt"></i>
                        <strong>تاريخ الفاتورة:</strong> {{ sale.sale_date|date:"d/m/Y" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer and Sale Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-section">
                    <div class="info-title">
                        <i class="fas fa-user"></i> معلومات العميل
                    </div>
                    <div class="info-item">
                        <strong>الاسم:</strong> {{ sale.customer.name }}
                    </div>
                    {% if sale.customer.phone %}
                    <div class="info-item">
                        <strong>الهاتف:</strong>
                        <i class="fas fa-phone text-muted"></i> {{ sale.customer.phone }}
                    </div>
                    {% endif %}
                    {% if sale.customer.email %}
                    <div class="info-item">
                        <strong>البريد الإلكتروني:</strong>
                        <i class="fas fa-envelope text-muted"></i> {{ sale.customer.email }}
                    </div>
                    {% endif %}
                    {% if sale.customer.address %}
                    <div class="info-item">
                        <strong>العنوان:</strong>
                        <i class="fas fa-map-marker-alt text-muted"></i> {{ sale.customer.address }}
                    </div>
                    {% endif %}
                    {% if sale.customer.city %}
                    <div class="info-item">
                        <strong>المدينة:</strong> {{ sale.customer.city }}
                    </div>
                    {% endif %}
                    {% if sale.customer.tax_number %}
                    <div class="info-item">
                        <strong>الرقم الضريبي:</strong>
                        <i class="fas fa-receipt text-muted"></i> {{ sale.customer.tax_number }}
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-section">
                    <div class="info-title">
                        <i class="fas fa-shopping-cart"></i> تفاصيل البيع
                    </div>
                    <div class="info-item">
                        <strong>نوع البيع:</strong>
                        <span class="badge bg-info">
                            {% if sale.sale_type == 'cash' %}نقدي
                            {% elif sale.sale_type == 'credit' %}آجل
                            {% elif sale.sale_type == 'installment' %}تقسيط
                            {% elif sale.sale_type == 'wholesale' %}جملة
                            {% else %}{{ sale.get_sale_type_display }}{% endif %}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>حالة البيع:</strong>
                        <span class="status-badge status-{{ sale.status }}">
                            {% if sale.status == 'completed' %}مكتمل
                            {% elif sale.status == 'pending' %}معلق
                            {% elif sale.status == 'cancelled' %}ملغي
                            {% else %}{{ sale.get_status_display }}{% endif %}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>حالة الدفع:</strong>
                        <span class="status-badge payment-{{ sale.payment_status }}">
                            {% if sale.payment_status == 'paid' %}مدفوع
                            {% elif sale.payment_status == 'partial' %}مدفوع جزئياً
                            {% elif sale.payment_status == 'unpaid' %}غير مدفوع
                            {% elif sale.payment_status == 'overdue' %}متأخر
                            {% else %}{{ sale.get_payment_status_display }}{% endif %}
                        </span>
                    </div>
                    {% if sale.due_date %}
                    <div class="info-item">
                        <strong>تاريخ الاستحقاق:</strong>
                        <i class="fas fa-calendar-check text-muted"></i> {{ sale.due_date|date:"d/m/Y" }}
                    </div>
                    {% endif %}
                    <div class="info-item">
                        <strong>تاريخ الإنشاء:</strong>
                        <i class="fas fa-clock text-muted"></i> {{ sale.sale_date|date:"d/m/Y H:i" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <div class="items-table">
            <table class="table table-bordered mb-0">
                <thead>
                    <tr>
                        <th style="width: 5%;">#</th>
                        <th style="width: 35%;">المنتج</th>
                        <th style="width: 10%;">الكمية</th>
                        <th style="width: 15%;">سعر الوحدة</th>
                        <th style="width: 10%;">الخصم %</th>
                        <th style="width: 15%;">مبلغ الخصم</th>
                        <th style="width: 15%;">الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in sale.items.all %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td class="text-start">
                            <div class="product-name">{{ item.product.name }}</div>
                            {% if item.product.sku %}
                            <div class="product-sku">
                                <i class="fas fa-barcode"></i> كود المنتج: {{ item.product.sku }}
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ item.quantity }}</span>
                        </td>
                        <td>{{ item.unit_price|floatformat:2 }} ج.م</td>
                        <td>
                            {% if item.discount_percentage > 0 %}
                                <span class="badge bg-warning">{{ item.discount_percentage|floatformat:1 }}%</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if item.discount_amount > 0 %}
                                <span class="text-danger">{{ item.discount_amount|floatformat:2 }} ج.م</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <strong class="text-success">{{ item.total_price|floatformat:2 }} ج.م</strong>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <br>لا توجد عناصر في هذه الفاتورة
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Totals and Notes Section -->
        <div class="row">
            <div class="col-md-6">
                {% if sale.notes %}
                <div class="notes-section">
                    <div class="notes-title">
                        <i class="fas fa-sticky-note"></i> ملاحظات
                    </div>
                    <p class="mb-0">{{ sale.notes|linebreaks }}</p>
                </div>
                {% endif %}
            </div>
            <div class="col-md-6">
                <div class="totals-section">
                    <div class="total-row">
                        <span><i class="fas fa-calculator"></i> المجموع الفرعي:</span>
                        <span><strong>{{ sale.subtotal|floatformat:2 }} ج.م</strong></span>
                    </div>
                    {% if sale.discount_amount > 0 %}
                    <div class="total-row">
                        <span><i class="fas fa-percentage"></i> إجمالي الخصم:</span>
                        <span class="text-danger"><strong>{{ sale.discount_amount|floatformat:2 }} ج.م</strong></span>
                    </div>
                    {% endif %}
                    {% if sale.tax_amount > 0 %}
                    <div class="total-row">
                        <span><i class="fas fa-receipt"></i> الضريبة:</span>
                        <span><strong>{{ sale.tax_amount|floatformat:2 }} ج.م</strong></span>
                    </div>
                    {% endif %}
                    <div class="total-row">
                        <span><i class="fas fa-money-bill-wave"></i> المبلغ الإجمالي:</span>
                        <span><strong>{{ sale.total_amount|floatformat:2 }} ج.م</strong></span>
                    </div>
                    {% if sale.paid_amount > 0 %}
                    <div class="total-row">
                        <span><i class="fas fa-hand-holding-usd"></i> المبلغ المدفوع:</span>
                        <span class="text-success"><strong>{{ sale.paid_amount|floatformat:2 }} ج.م</strong></span>
                    </div>
                    {% endif %}
                    {% if sale.balance_amount > 0 %}
                    <div class="total-row">
                        <span><i class="fas fa-balance-scale"></i> الرصيد المتبقي:</span>
                        <span class="text-warning"><strong>{{ sale.balance_amount|floatformat:2 }} ج.م</strong></span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Payment History -->
        {% if sale.payments.exists %}
        <div class="payment-history">
            <div class="payment-history-title">
                <i class="fas fa-credit-card"></i> تاريخ المدفوعات
            </div>
            <div class="table-responsive">
                <table class="table payment-table mb-0">
                    <thead>
                        <tr>
                            <th><i class="fas fa-hashtag"></i> رقم الدفعة</th>
                            <th><i class="fas fa-money-bill"></i> المبلغ</th>
                            <th><i class="fas fa-credit-card"></i> طريقة الدفع</th>
                            <th><i class="fas fa-calendar"></i> التاريخ</th>
                            <th><i class="fas fa-user"></i> المستلم</th>
                            <th><i class="fas fa-info-circle"></i> الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in sale.payments.all %}
                        <tr>
                            <td>
                                <span class="badge bg-info">{{ payment.payment_number }}</span>
                            </td>
                            <td>
                                <strong class="text-success">{{ payment.amount|floatformat:2 }} ج.م</strong>
                            </td>
                            <td>
                                <span class="badge bg-secondary">
                                    {% if payment.payment_method == 'cash' %}
                                        <i class="fas fa-money-bill"></i> نقدي
                                    {% elif payment.payment_method == 'card' %}
                                        <i class="fas fa-credit-card"></i> بطاقة ائتمان
                                    {% elif payment.payment_method == 'bank_transfer' %}
                                        <i class="fas fa-university"></i> تحويل بنكي
                                    {% elif payment.payment_method == 'check' %}
                                        <i class="fas fa-money-check"></i> شيك
                                    {% elif payment.payment_method == 'mobile_payment' %}
                                        <i class="fas fa-mobile-alt"></i> دفع محمول
                                    {% elif payment.payment_method == 'credit' %}
                                        <i class="fas fa-gift"></i> رصيد المتجر
                                    {% else %}{{ payment.get_payment_method_display }}{% endif %}
                                </span>
                            </td>
                            <td>
                                <i class="fas fa-clock text-muted"></i>
                                {{ payment.payment_date|date:"d/m/Y H:i" }}
                            </td>
                            <td>
                                <i class="fas fa-user text-muted"></i>
                                {{ payment.received_by.get_full_name|default:payment.received_by.username }}
                            </td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i> مكتمل
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Invoice Footer -->
        <div class="invoice-footer">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>شكراً لتعاملكم معنا</strong></p>
                    <p>{{ company_info.name }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>للاستفسارات:</strong></p>
                    <p>{{ company_info.phone }}</p>
                    <p>{{ company_info.email }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>تم إنشاء الفاتورة:</strong></p>
                    <p>{{ sale.sale_date|date:"d/m/Y H:i" }}</p>
                    <p><small>نظام SpareSmart لإدارة قطع الغيار</small></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'sales:sale_print' sale.id %}" class="action-btn btn-primary-custom" target="_blank">
            <i class="fas fa-print"></i> طباعة الفاتورة
        </a>
        <a href="{% url 'sales:sale_detail' sale.id %}" class="action-btn btn-info-custom">
            <i class="fas fa-eye"></i> تفاصيل البيع
        </a>
        {% if sale.payment_status != 'paid' %}
        <a href="{% url 'sales:payment_create' sale.id %}" class="action-btn btn-success-custom">
            <i class="fas fa-credit-card"></i> إضافة دفعة
        </a>
        {% endif %}
        <a href="{% url 'sales:sale_list' %}" class="action-btn btn-secondary-custom">
            <i class="fas fa-list"></i> قائمة المبيعات
        </a>
    </div>
</div>

<!-- JavaScript for enhanced interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Add loading state to action buttons
    document.querySelectorAll('.action-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.target || this.target !== '_blank') {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
            }
        });
    });

    // Add tooltip functionality for status badges
    const statusBadges = document.querySelectorAll('.status-badge');
    statusBadges.forEach(badge => {
        badge.setAttribute('title', 'اضغط للمزيد من التفاصيل');
        badge.style.cursor = 'help';
    });

    // Add print shortcut
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.open('{% url "sales:sale_print" sale.id %}', '_blank');
        }
    });
});
</script>
{% endblock %}
