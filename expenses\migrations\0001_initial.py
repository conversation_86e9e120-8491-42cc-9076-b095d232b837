# Generated by Django 4.2.7 on 2025-09-14 12:21

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expense_number', models.CharField(max_length=50, unique=True)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('status', models.<PERSON><PERSON><PERSON><PERSON>(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('paid', 'Paid'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20)),
                ('requires_approval', models.BooleanField(default=False)),
                ('expense_date', models.DateField(default=django.utils.timezone.now)),
                ('due_date', models.DateField(blank=True, null=True)),
                ('paid_date', models.DateField(blank=True, null=True)),
                ('payment_method', models.CharField(blank=True, choices=[('cash', 'Cash'), ('bank_transfer', 'Bank Transfer'), ('check', 'Check'), ('credit_card', 'Credit Card'), ('petty_cash', 'Petty Cash'), ('online', 'Online Payment')], max_length=20)),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('vendor_name', models.CharField(blank=True, max_length=200)),
                ('vendor_contact', models.CharField(blank=True, max_length=100)),
                ('is_tax_deductible', models.BooleanField(default=False)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('account_code', models.CharField(blank=True, max_length=50)),
                ('receipt_image', models.ImageField(blank=True, null=True, upload_to='expense_receipts/')),
                ('invoice_file', models.FileField(blank=True, null=True, upload_to='expense_invoices/')),
                ('additional_documents', models.FileField(blank=True, null=True, upload_to='expense_docs/')),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('approval_notes', models.TextField(blank=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('notes', models.TextField(blank=True)),
                ('is_recurring', models.BooleanField(default=False)),
                ('recurring_frequency', models.CharField(blank=True, max_length=20)),
                ('next_due_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_expenses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Expense',
                'verbose_name_plural': 'Expenses',
                'db_table': 'expenses',
                'ordering': ['-expense_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('category_type', models.CharField(choices=[('operational', 'Operational'), ('administrative', 'Administrative'), ('marketing', 'Marketing'), ('maintenance', 'Maintenance'), ('utilities', 'Utilities'), ('travel', 'Travel'), ('equipment', 'Equipment'), ('other', 'Other')], default='operational', max_length=20)),
                ('budget_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('requires_approval', models.BooleanField(default=False)),
                ('approval_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Expense Category',
                'verbose_name_plural': 'Expense Categories',
                'db_table': 'expense_categories',
                'ordering': ['category_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='RecurringExpense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('next_due_date', models.DateField()),
                ('last_generated_date', models.DateField(blank=True, null=True)),
                ('auto_generate', models.BooleanField(default=True)),
                ('auto_approve', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('active', 'Active'), ('paused', 'Paused'), ('expired', 'Expired'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('vendor_name', models.CharField(blank=True, max_length=200)),
                ('payment_method', models.CharField(blank=True, choices=[('cash', 'Cash'), ('bank_transfer', 'Bank Transfer'), ('check', 'Check'), ('credit_card', 'Credit Card'), ('petty_cash', 'Petty Cash'), ('online', 'Online Payment')], max_length=20)),
                ('account_code', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='expenses.expensecategory')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Recurring Expense',
                'verbose_name_plural': 'Recurring Expenses',
                'db_table': 'recurring_expenses',
                'ordering': ['next_due_date'],
            },
        ),
        migrations.CreateModel(
            name='PettyCash',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_number', models.CharField(max_length=50, unique=True)),
                ('transaction_type', models.CharField(choices=[('opening', 'Opening Balance'), ('addition', 'Cash Addition'), ('expense', 'Expense Payment'), ('return', 'Cash Return'), ('reconciliation', 'Reconciliation')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=10)),
                ('description', models.TextField()),
                ('transaction_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('expense', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='petty_cash_transactions', to='expenses.expense')),
                ('handled_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Petty Cash Transaction',
                'verbose_name_plural': 'Petty Cash Transactions',
                'db_table': 'petty_cash',
                'ordering': ['-transaction_date'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('approved', 'Approved'), ('rejected', 'Rejected'), ('returned', 'Returned for Revision')], max_length=20)),
                ('comments', models.TextField(blank=True)),
                ('approval_date', models.DateTimeField(auto_now_add=True)),
                ('approver', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('expense', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approvals', to='expenses.expense')),
            ],
            options={
                'verbose_name': 'Expense Approval',
                'verbose_name_plural': 'Expense Approvals',
                'db_table': 'expense_approvals',
                'ordering': ['-approval_date'],
            },
        ),
        migrations.AddField(
            model_name='expense',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='expenses', to='expenses.expensecategory'),
        ),
        migrations.AddField(
            model_name='expense',
            name='paid_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='paid_expenses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='expense',
            name='requested_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='requested_expenses', to=settings.AUTH_USER_MODEL),
        ),
    ]
