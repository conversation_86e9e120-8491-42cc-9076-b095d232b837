{% extends 'base.html' %}

{% block title %}{{ title }} - SpareSmart{% endblock %}

{% block extra_css %}
<style>
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .delete-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }
    .item-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border-right: 4px solid #dc3545;
    }
    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        color: white;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    .btn-delete:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        color: white;
    }
    .btn-cancel {
        background: #6c757d;
        border: none;
        color: white;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Delete Header -->
    <div class="delete-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">{{ title }}</h2>
                <p class="mb-0 opacity-75">تأكيد حذف العنصر من الفاتورة</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'inventory:invoice_detail' invoice.id %}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للفاتورة
                </a>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="delete-card">
        <div class="text-center">
            <i class="fas fa-exclamation-triangle warning-icon"></i>
            <h4 class="text-danger mb-3">تحذير: حذف عنصر من الفاتورة</h4>
            <p class="text-muted mb-4">
                هل أنت متأكد من أنك تريد حذف هذا العنصر من الفاتورة؟ 
                <br>هذا الإجراء لا يمكن التراجع عنه.
            </p>
        </div>

        <!-- Item Details -->
        <div class="item-details">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-danger mb-3">تفاصيل العنصر المراد حذفه:</h6>
                    <p><strong>المنتج:</strong> {{ item.product_name }}</p>
                    <p><strong>رمز المنتج:</strong> {{ item.product_sku }}</p>
                    <p><strong>الكمية:</strong> {{ item.quantity|floatformat:2 }} {{ item.unit_name }}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-danger mb-3">التفاصيل المالية:</h6>
                    <p><strong>سعر الوحدة:</strong> {{ item.unit_price|floatformat:2 }} ج.م</p>
                    {% if item.discount_percentage > 0 %}
                        <p><strong>الخصم:</strong> {{ item.discount_percentage|floatformat:1 }}% ({{ item.discount_amount|floatformat:2 }} ج.م)</p>
                    {% endif %}
                    <p><strong>الإجمالي:</strong> <span class="fw-bold text-danger">{{ item.total_price|floatformat:2 }} ج.م</span></p>
                </div>
            </div>
            {% if item.notes %}
                <div class="mt-3">
                    <p><strong>الملاحظات:</strong> {{ item.notes }}</p>
                </div>
            {% endif %}
        </div>

        <!-- Invoice Info -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="bg-light p-3 rounded">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}
                        </div>
                        <div class="col-md-3">
                            <strong>النوع:</strong> {{ invoice.get_invoice_type_display }}
                        </div>
                        <div class="col-md-3">
                            <strong>{{ invoice.is_sale|yesno:"العميل,المورد" }}:</strong> {{ invoice.client_name }}
                        </div>
                        <div class="col-md-3">
                            <strong>الحالة:</strong> 
                            <span class="badge bg-secondary">{{ invoice.get_status_display }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Form -->
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'inventory:invoice_detail' invoice.id %}" class="btn btn-cancel">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-delete">
                    <i class="fas fa-trash me-2"></i>تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add confirmation dialog
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد من حذف هذا العنصر من الفاتورة؟')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
