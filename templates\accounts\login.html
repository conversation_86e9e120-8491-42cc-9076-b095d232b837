{% extends 'base.html' %}

{% block title %}Login - SpareSmart{% endblock %}

{% block login_content %}
<div class="login-wrapper">
    <style>
        .login-wrapper {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 300;
            font-size: 2rem;
        }
        
        .login-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 40px 30px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-control {
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            padding: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            color: white;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
            color: white;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            margin-top: 20px;
        }
        
        .remember-me input {
            margin-right: 10px;
        }
        
        .login-footer {
            text-align: center;
            padding: 20px 30px;
            background: #f8f9fa;
            color: #6c757d;
        }
        
        .system-info {
            margin-top: 30px;
            text-align: center;
        }
        
        .system-info .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            margin: 0 5px;
        }
        
        .features {
            margin-top: 40px;
            text-align: center;
        }
        
        .features .feature {
            color: white;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .features .feature i {
            margin-right: 10px;
            width: 20px;
        }
    </style>
    
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-card">
                    <div class="login-header">
                        <h2><i class="fas fa-cogs"></i> SpareSmart</h2>
                        <p>Spare Parts Management System</p>
                        
                        <div class="features">
                            <div class="feature">
                                <i class="fas fa-boxes"></i> Inventory Management
                            </div>
                            <div class="feature">
                                <i class="fas fa-chart-line"></i> Sales & Purchase Tracking
                            </div>
                            <div class="feature">
                                <i class="fas fa-file-invoice"></i> Invoice Generation
                            </div>
                            <div class="feature">
                                <i class="fas fa-credit-card"></i> Installment Management
                            </div>
                        </div>
                        
                        <div class="system-info">
                            <span class="badge">Motorcycles</span>
                            <span class="badge">Cars</span>
                            <span class="badge">Tuk-Tuks</span>
                        </div>
                    </div>
                    
                    <div class="login-body">
                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="form-group">
                                <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.username.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                                {{ form.password }}
                                {% if form.password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.password.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="remember-me">
                                {{ form.remember_me }}
                                <label for="{{ form.remember_me.id_for_label }}" class="form-label">Remember me</label>
                            </div>
                            
                            <button type="submit" class="btn btn-login">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </button>
                        </form>
                    </div>
                    
                    <div class="login-footer">
                        <small>&copy; 2024 SpareSmart. All rights reserved.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Add loading animation on form submit
    document.querySelector('form').addEventListener('submit', function() {
        const btn = document.querySelector('.btn-login');
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
        btn.disabled = true;
    });
    
    // Focus on username field
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelector('input[name="username"]').focus();
    });
</script>
{% endblock %}