"""
Django settings for sparesmart project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import os
from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', default='django-insecure-tb4s-85tkex60++u#q6re+$k)*0mr1cz+3*#&2e&w-3qxqly5!')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=True, cast=bool)

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'crispy_forms',
    'crispy_bootstrap4',
    'widget_tweaks',
    'django_filters',
    # Project apps
    'accounts',
    'inventory',
    'sales',
    'purchases',
    'expenses',
    'reports',
    'dashboard',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',  # Must be after SessionMiddleware
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'sparesmart.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',  # Added for language support
            ],
        },
    },
]

WSGI_APPLICATION = 'sparesmart.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# For production with SQL Server, uncomment and configure:
# DATABASES = {
#     'default': {
#         'ENGINE': 'mssql',
#         'NAME': config('DATABASE_NAME', default='sparesmart'),
#         'USER': config('DATABASE_USER', default='admin'),
#         'PASSWORD': config('DATABASE_PASSWORD', default='hgslduhgfwdv'),
#         'HOST': config('DATABASE_HOST', default='DESKTOP-QTSU8TT\SQLEXPRESS'),
#         'PORT': config('DATABASE_PORT', default='1433'),
#         'OPTIONS': {
#             'driver': config('DATABASE_OPTIONS_driver', default='ODBC Driver 17 for SQL Server'),
#             'unicode_results': config('DATABASE_OPTIONS_unicode_results', default=True, cast=bool),
#             'autocommit': config('DATABASE_OPTIONS_autocommit', default=True, cast=bool),
#         },
#     }
# }


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'ar'  # Arabic as default language

# Available languages - العربية فقط
LANGUAGES = [
    ('ar', 'العربية'),
]

TIME_ZONE = 'Africa/Cairo'  # Cairo timezone for Arabic region

# Use fallback to prevent crashes if translation files are corrupted
USE_I18N = True
USE_L10N = True
USE_TZ = True

# Translation settings - معطل مؤقتاً لحل مشكلة ملف .mo
# LOCALE_PATHS = [
#     BASE_DIR / 'locale',
# ]

# Format localization
USE_THOUSAND_SEPARATOR = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Crispy Forms
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap4"
CRISPY_TEMPLATE_PACK = "bootstrap4"

# Authentication
LOGIN_URL = '/auth/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/auth/login/'

# Custom User Model
AUTH_USER_MODEL = 'accounts.User'

# Date and Time Format
DATE_FORMAT = 'Y-m-d'
DATETIME_FORMAT = 'Y-m-d H:i:s'
USE_L10N = True  # Enable localization for Arabic formatting

# Arabic Language Settings
LANGUAGE_BIDI = True  # Right-to-left text direction
USE_THOUSAND_SEPARATOR = True
THOUSAND_SEPARATOR = '،'  # Arabic thousands separator
DECIMAL_SEPARATOR = '.'

# Arabic Number Format
NUMBER_GROUPING = 3
FIRST_DAY_OF_WEEK = 6  # Saturday (Arabic week starts on Saturday)

# Arabic Date and Time Formats
DATE_FORMAT = 'Y/m/d'  # Arabic date format
DATETIME_FORMAT = 'Y/m/d H:i'
SHORT_DATE_FORMAT = 'm/d/Y'
SHORT_DATETIME_FORMAT = 'm/d/Y H:i'

# Enable Arabic locale formatting
USE_L10N = True
USE_TZ = True

# Pagination
PAGINATE_BY = 20

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
